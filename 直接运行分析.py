#!/usr/bin/env python3
"""
直接运行时间序列数据分析
不依赖Jupyter，直接生成HTML报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, mean_squared_error, r2_score, accuracy_score
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from scipy.stats import f_oneway
import os
import base64
from io import BytesIO

# 设置
warnings.filterwarnings('ignore')
plt.style.use('default')
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 10

class HTMLReportGenerator:
    def __init__(self):
        self.html_content = []
        self.figure_count = 0
    
    def add_title(self, title, level=1):
        self.html_content.append(f"<h{level}>{title}</h{level}>")
    
    def add_text(self, text):
        self.html_content.append(f"<p>{text}</p>")
    
    def add_code_output(self, output):
        self.html_content.append(f"<pre style='background-color: #f5f5f5; padding: 10px; border-radius: 5px;'>{output}</pre>")
    
    def add_figure(self, fig):
        """将matplotlib图形转换为base64并添加到HTML"""
        buffer = BytesIO()
        fig.savefig(buffer, format='png', dpi=100, bbox_inches='tight')
        buffer.seek(0)
        image_base64 = base64.b64encode(buffer.getvalue()).decode()
        buffer.close()
        plt.close(fig)
        
        self.figure_count += 1
        self.html_content.append(f'<div style="text-align: center; margin: 20px 0;">')
        self.html_content.append(f'<img src="data:image/png;base64,{image_base64}" style="max-width: 100%; height: auto;">')
        self.html_content.append(f'</div>')
    
    def save_html(self, filename):
        """保存HTML文件"""
        html_template = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>高维时间序列数据分析</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }}
        h1 {{ color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }}
        h2 {{ color: #34495e; margin-top: 30px; }}
        h3 {{ color: #7f8c8d; }}
        pre {{ background-color: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }}
        .container {{ max-width: 1200px; margin: 0 auto; }}
    </style>
</head>
<body>
    <div class="container">
        {''.join(self.html_content)}
    </div>
</body>
</html>
"""
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_template)

def run_analysis():
    """运行完整分析"""
    
    # 初始化HTML生成器
    report = HTMLReportGenerator()
    
    # 标题
    report.add_title("高维时间序列数据分析")
    report.add_text("本报告对包含56个特征的高维时间序列数据进行全面分析，探索数据特征、变量关系和预测能力。")
    
    try:
        # 1. 数据加载
        report.add_title("1. 数据加载和基本信息", 2)
        
        data_file = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'
        df = pd.read_csv(data_file)
        
        report.add_code_output(f"数据维度: {df.shape}")
        report.add_code_output(f"列名: {list(df.columns)}")
        
        # 数据质量检查
        missing_data = df.isnull().sum()
        if missing_data.sum() == 0:
            report.add_code_output("✓ 数据完整，无缺失值")
        else:
            report.add_code_output(f"缺失值统计:\n{missing_data[missing_data > 0]}")
        
        # 第10列分析
        class_dist = df['10'].value_counts().sort_index()
        report.add_code_output(f"第10列（分类变量）分布:\n{class_dist}")
        
        continuous_vars = [col for col in df.columns if col != '10']
        report.add_code_output(f"连续变量数量: {len(continuous_vars)}")
        
        # 2. 数据探索
        report.add_title("2. 数据探索分析", 2)
        
        # 时间序列图
        df['time_idx'] = range(len(df))
        sample_vars = ['1', '5', '15', '25', '35', '45']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        axes = axes.flatten()
        
        for i, var in enumerate(sample_vars):
            axes[i].plot(df['time_idx'], df[var], linewidth=0.8, alpha=0.8)
            axes[i].set_title(f'变量 {var} 时间序列')
            axes[i].set_xlabel('时间')
            axes[i].grid(True, alpha=0.3)
        
        plt.tight_layout()
        report.add_figure(fig)
        
        # 分类变量分布
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        colors = ['blue', 'red', 'green']
        for i, class_val in enumerate(sorted(df['10'].unique())):
            mask = df['10'] == class_val
            ax1.scatter(df.loc[mask, 'time_idx'], df.loc[mask, '10'], 
                       c=colors[i], alpha=0.6, s=1, label=f'类别 {class_val}')
        
        ax1.set_title('分类变量时间分布')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('类别')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        class_counts = df['10'].value_counts().sort_index()
        bars = ax2.bar(class_counts.index, class_counts.values, 
                       color=['skyblue', 'lightcoral', 'lightgreen'], alpha=0.8)
        ax2.set_title('类别频数分布')
        ax2.set_xlabel('类别')
        ax2.set_ylabel('频数')
        
        for bar, count in zip(bars, class_counts.values):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20, 
                     str(count), ha='center', va='bottom')
        
        plt.tight_layout()
        report.add_figure(fig)
        
        # 3. 相关性分析
        report.add_title("3. 变量关系分析", 2)
        
        corr_matrix = df[continuous_vars].corr()
        
        fig, ax = plt.subplots(figsize=(16, 14))
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, cmap='RdBu_r', center=0,
                    square=True, linewidths=0.1, cbar_kws={"shrink": .8})
        plt.title('变量相关性矩阵', fontsize=16, pad=20)
        plt.tight_layout()
        report.add_figure(fig)
        
        # 强相关变量对
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if abs(corr_val) > 0.7:
                    high_corr_pairs.append((corr_matrix.columns[i], 
                                          corr_matrix.columns[j], corr_val))
        
        report.add_code_output(f"发现 {len(high_corr_pairs)} 对强相关变量 (|r| > 0.7)")
        if high_corr_pairs:
            for var1, var2, corr in high_corr_pairs[:10]:
                report.add_code_output(f"  {var1} - {var2}: {corr:.3f}")
        
        # 4. PCA分析
        report.add_title("4. 降维分析", 2)
        
        scaler = StandardScaler()
        scaled_data = scaler.fit_transform(df[continuous_vars].fillna(df[continuous_vars].mean()))
        
        pca = PCA()
        pca_result = pca.fit_transform(scaled_data)
        cumvar_ratio = np.cumsum(pca.explained_variance_ratio_)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        ax1.bar(range(1, 21), pca.explained_variance_ratio_[:20], alpha=0.8)
        ax1.set_title('前20个主成分解释方差比')
        ax1.set_xlabel('主成分')
        ax1.set_ylabel('解释方差比')
        ax1.grid(True, alpha=0.3)
        
        ax2.plot(range(1, 51), cumvar_ratio[:50], 'o-', markersize=4)
        ax2.axhline(y=0.8, color='red', linestyle='--', label='80%')
        ax2.axhline(y=0.9, color='orange', linestyle='--', label='90%')
        ax2.set_title('累积解释方差比')
        ax2.set_xlabel('主成分数量')
        ax2.set_ylabel('累积解释方差比')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        report.add_figure(fig)
        
        n_comp_80 = np.argmax(cumvar_ratio >= 0.8) + 1
        n_comp_90 = np.argmax(cumvar_ratio >= 0.9) + 1
        
        report.add_code_output(f"解释80%方差需要: {n_comp_80} 个主成分")
        report.add_code_output(f"解释90%方差需要: {n_comp_90} 个主成分")
        
        # 5. 机器学习建模
        report.add_title("5. 机器学习建模", 2)
        
        # 分类模型
        X = df[continuous_vars].fillna(df[continuous_vars].mean())
        y = df['10']
        
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        scaler_ml = StandardScaler()
        X_train_scaled = scaler_ml.fit_transform(X_train)
        X_test_scaled = scaler_ml.transform(X_test)
        
        rf_clf = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
        rf_clf.fit(X_train_scaled, y_train)
        
        y_pred = rf_clf.predict(X_test_scaled)
        accuracy = accuracy_score(y_test, y_pred)
        
        report.add_code_output(f"分类模型准确率: {accuracy:.3f}")
        
        # 特征重要性
        feature_importance = pd.DataFrame({
            '变量': continuous_vars,
            '重要性': rf_clf.feature_importances_
        }).sort_values('重要性', ascending=False)
        
        fig, ax = plt.subplots(figsize=(12, 8))
        top_features = feature_importance.head(20)
        ax.barh(range(len(top_features)), top_features['重要性'], alpha=0.8)
        ax.set_yticks(range(len(top_features)))
        ax.set_yticklabels([f'变量{var}' for var in top_features['变量']])
        ax.set_title('特征重要性排名 (前20个)', fontsize=14)
        ax.set_xlabel('重要性得分')
        ax.invert_yaxis()
        ax.grid(True, alpha=0.3)
        plt.tight_layout()
        report.add_figure(fig)
        
        report.add_code_output(f"最重要的5个变量: {list(top_features['变量'].head(5))}")
        
        # 6. 总结
        report.add_title("6. 分析总结", 2)
        
        summary_text = f"""
        <h3>主要发现:</h3>
        <ul>
        <li>数据规模: {df.shape[0]} 个观测点, {len(continuous_vars)} 个连续变量</li>
        <li>数据质量: 完整无缺失值</li>
        <li>强相关变量对: {len(high_corr_pairs)} 对</li>
        <li>降维效果: {n_comp_80} 个主成分可解释80%方差</li>
        <li>分类准确率: {accuracy:.3f}</li>
        </ul>
        
        <h3>业务建议:</h3>
        <ul>
        <li>利用PCA降维技术优化计算效率</li>
        <li>重点关注特征重要性排名靠前的变量</li>
        <li>建立模型监控机制确保持续性能</li>
        <li>考虑集成多个模型提升预测稳定性</li>
        </ul>
        """
        
        report.html_content.append(summary_text)
        
        # 保存HTML文件
        output_file = '/Users/<USER>/Desktop/Cui_Wei.html'
        report.save_html(output_file)
        
        print(f"✓ 分析完成！")
        print(f"✓ HTML文件已生成: {output_file}")
        
        # 检查文件大小
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file) / (1024 * 1024)
            print(f"✓ 文件大小: {file_size:.2f} MB")
        
        return True
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    print("开始执行时间序列数据分析...")
    print("=" * 50)
    
    success = run_analysis()
    
    if success:
        print("\\n分析成功完成！")
        print("请检查桌面上的 Cui_Wei.html 文件")
    else:
        print("\\n分析失败，请检查错误信息")
