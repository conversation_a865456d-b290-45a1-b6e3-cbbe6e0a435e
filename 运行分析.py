#!/usr/bin/env python3
"""
高维时间序列数据分析 - 一键运行脚本
此脚本将执行完整的分析流程并生成HTML报告
"""

import subprocess
import sys
import os
import time

def check_environment():
    """检查环境是否准备就绪"""
    print("=" * 50)
    print("检查运行环境...")
    print("=" * 50)
    
    # 检查数据文件
    data_path = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'
    if not os.path.exists(data_path):
        print(f"❌ 错误：找不到数据文件 {data_path}")
        print("请确认数据文件是否存在于桌面")
        return False
    else:
        print(f"✅ 数据文件存在: {data_path}")
    
    # 检查notebook文件
    notebook_path = '高维时间序列数据分析_修正版.ipynb'
    if not os.path.exists(notebook_path):
        print(f"❌ 错误：找不到notebook文件 {notebook_path}")
        return False
    else:
        print(f"✅ Notebook文件存在: {notebook_path}")
    
    # 检查必要的库
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn', 'sklearn', 'scipy', 'statsmodels']
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
            else:
                __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\\n缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    return True

def run_notebook():
    """运行notebook并生成HTML"""
    print("\\n" + "=" * 50)
    print("开始执行数据分析...")
    print("=" * 50)
    
    notebook_file = '高维时间序列数据分析_修正版.ipynb'
    output_file = '/Users/<USER>/Desktop/Cui_Wei.html'
    
    try:
        # 执行notebook
        print("正在执行notebook分析...")
        cmd = [
            'jupyter', 'nbconvert', 
            '--to', 'html',
            '--execute',
            '--output', output_file,
            notebook_file
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)  # 10分钟超时
        
        if result.returncode == 0:
            print(f"✅ 分析完成！HTML文件已生成: {output_file}")
            
            # 检查文件大小
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)  # MB
                print(f"📊 文件大小: {file_size:.2f} MB")
                return True
            else:
                print("❌ HTML文件未生成")
                return False
        else:
            print(f"❌ 执行失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 执行超时（超过10分钟）")
        return False
    except FileNotFoundError:
        print("❌ 找不到jupyter命令，请确保已安装Jupyter")
        print("可以运行: pip install jupyter")
        return False
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 高维时间序列数据分析 - 自动化运行脚本")
    print("📝 此脚本将执行完整的数据分析并生成HTML报告")
    
    # 检查环境
    if not check_environment():
        print("\\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    print("\\n✅ 环境检查通过！")
    
    # 询问用户是否继续
    response = input("\\n是否开始执行分析？(y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("分析已取消")
        return
    
    # 记录开始时间
    start_time = time.time()
    
    # 运行分析
    success = run_notebook()
    
    # 计算耗时
    end_time = time.time()
    duration = end_time - start_time
    
    print("\\n" + "=" * 50)
    print("执行结果汇总")
    print("=" * 50)
    
    if success:
        print("🎉 分析成功完成！")
        print(f"⏱️  总耗时: {duration:.1f} 秒")
        print("📁 输出文件: /Users/<USER>/Desktop/Cui_Wei.html")
        print("\\n📋 下一步:")
        print("1. 检查生成的HTML文件")
        print("2. 确认所有图表和分析结果正确显示")
        print("3. 提交HTML文件")
    else:
        print("❌ 分析执行失败")
        print(f"⏱️  耗时: {duration:.1f} 秒")
        print("\\n🔧 故障排除建议:")
        print("1. 检查数据文件是否存在且格式正确")
        print("2. 确认所有依赖包已正确安装")
        print("3. 检查系统内存是否充足")
        print("4. 查看错误信息并相应处理")

if __name__ == "__main__":
    main()
