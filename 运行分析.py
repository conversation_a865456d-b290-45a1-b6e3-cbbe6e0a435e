#!/usr/bin/env python3
"""
时间序列数据分析 - 最终运行脚本
自动检测环境并选择最佳执行方式
"""

import subprocess
import sys
import os
import time

def check_data_file():
    """检查数据文件"""
    data_path = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'
    if not os.path.exists(data_path):
        print(f"错误：找不到数据文件 {data_path}")
        return False

    try:
        import pandas as pd
        df = pd.read_csv(data_path)
        print(f"✓ 数据文件检查通过 (形状: {df.shape})")
        return True
    except Exception as e:
        print(f"错误：数据文件读取失败 - {e}")
        return False

def check_packages():
    """检查必要的包"""
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn', 'sklearn', 'scipy', 'statsmodels']
    missing_packages = []

    for package in required_packages:
        try:
            if package == 'sklearn':
                __import__('sklearn')
            else:
                __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print(f"缺失包: {', '.join(missing_packages)}")
        return False
    else:
        print("✓ 所有必要包已安装")
        return True

def check_jupyter():
    """检查Jupyter是否可用"""
    try:
        result = subprocess.run([sys.executable, '-m', 'jupyter', '--version'],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Jupyter可用")
            return True
        else:
            print("✗ Jupyter不可用")
            return False
    except:
        print("✗ Jupyter未安装")
        return False

def run_direct_analysis():
    """运行直接分析脚本"""
    try:
        print("使用直接分析方式...")
        result = subprocess.run([sys.executable, '直接运行分析.py'],
                              capture_output=True, text=True, timeout=600)

        if result.returncode == 0:
            print("✓ 直接分析完成")
            return True
        else:
            print(f"直接分析失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"直接分析错误: {e}")
        return False

def run_jupyter_analysis():
    """使用Jupyter运行分析"""
    try:
        notebook_file = '时间序列数据分析.ipynb'
        desktop_path = '/Users/<USER>/Desktop'

        cmd = [
            sys.executable, '-m', 'jupyter', 'nbconvert',
            '--to', 'html',
            '--execute',
            '--output-dir', desktop_path,
            '--output', 'Cui_Wei',
            notebook_file
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=900)

        if result.returncode == 0:
            output_file = os.path.join(desktop_path, 'Cui_Wei.html')
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file) / (1024 * 1024)
                print(f"✓ Jupyter分析完成")
                print(f"✓ 文件大小: {file_size:.2f} MB")
                return True

        print("Jupyter分析失败")
        return False

    except Exception as e:
        print(f"Jupyter分析错误: {e}")
        return False

def main():
    """主函数"""
    print("时间序列数据分析 - 自动运行脚本")
    print("=" * 50)

    # 检查基本环境
    print("检查运行环境...")

    if not check_data_file():
        print("\\n请确保数据文件存在于桌面")
        return

    if not check_packages():
        print("\\n请安装缺失的包: pip install pandas numpy matplotlib seaborn scikit-learn scipy statsmodels")
        return

    print("\\n基本环境检查通过")

    # 询问是否继续
    response = input("\\n开始执行分析? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是', '']:
        print("已取消")
        return

    # 记录开始时间
    start_time = time.time()

    # 选择执行方式
    success = False

    # 首先尝试直接分析（推荐）
    print("\\n尝试直接分析方式...")
    if os.path.exists('直接运行分析.py'):
        success = run_direct_analysis()

    # 如果直接分析失败，尝试Jupyter方式
    if not success and check_jupyter():
        print("\\n尝试Jupyter方式...")
        success = run_jupyter_analysis()

    # 计算耗时
    end_time = time.time()
    duration = end_time - start_time

    print("\\n" + "=" * 50)
    print("执行结果")
    print("=" * 50)

    if success:
        print("✓ 分析完成")
        print(f"✓ 耗时: {duration:.1f} 秒")
        print("✓ 文件位置: /Users/<USER>/Desktop/Cui_Wei.html")
        print("\\n请检查生成的HTML文件并提交")
    else:
        print("✗ 分析失败")
        print(f"✗ 耗时: {duration:.1f} 秒")
        print("\\n建议:")
        print("1. 检查数据文件是否正确")
        print("2. 确认所有依赖包已安装")
        print("3. 检查系统资源是否充足")

if __name__ == "__main__":
    main()
