# 高维时间序列数据分析 - 运行指南

## 项目概述

本项目是针对Bastion Trading Data & Python Assignment笔试要求开发的高维时间序列数据分析解决方案。分析包含探索性数据分析、统计建模、机器学习预测、因果关系分析等多个维度。

## 文件结构

```
├── 高维时间序列数据分析_修正版.ipynb  # 主要分析notebook（修正版）
├── convert_notebook.py               # HTML转换脚本
├── 运行分析.py                       # 一键运行脚本（推荐）
├── 环境测试.py                       # 环境验证脚本
├── requirements.txt                  # 依赖包列表
├── 运行指南.md                       # 本文件
└── /Users/<USER>/Desktop/Cui_Wei.html # 最终提交文件（运行后生成到桌面）
```

## 环境要求

### Python版本
- Python 3.7+

### 必需的库
```bash
pip install pandas numpy matplotlib seaborn scipy scikit-learn statsmodels plotly jupyter nbconvert
```

或者使用requirements.txt（如果提供）：
```bash
pip install -r requirements.txt
```

## 数据准备

1. 确保数据文件 `TEST_Trader_Quant_dataset.csv` 位于路径：
   ```
   /Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv
   ```

2. 如果数据文件位置不同，请修改notebook中第一个代码单元格的路径：
   ```python
   data_path = '/your/actual/path/TEST_Trader_Quant_dataset.csv'
   ```

## 运行步骤

### 方法一：一键运行（推荐）

1. 运行环境检查：
   ```bash
   python 环境测试.py
   ```

2. 一键执行分析：
   ```bash
   python 运行分析.py
   ```

3. 检查桌面生成的 `Cui_Wei.html` 文件

### 方法二：手动运行Jupyter Notebook

1. 启动Jupyter Notebook：
   ```bash
   jupyter notebook
   ```

2. 打开 `高维时间序列数据分析_修正版.ipynb`

3. 依次运行所有代码单元格（建议使用 "Cell" → "Run All"）

4. 检查所有输出和图表是否正确显示

### 方法三：使用转换脚本

1. 首先确保notebook已经完全运行并保存

2. 运行转换脚本：
   ```bash
   python convert_notebook.py
   ```

3. 检查桌面生成的 `Cui_Wei.html` 文件

## 分析内容概览

### 1. 数据加载与初步探索
- 数据质量检查
- 基础统计描述
- 变量类型识别

### 2. 探索性数据分析（EDA）
- 时间序列趋势分析
- 离散变量分布分析
- 数据分布特征分析

### 3. 相关性分析与特征关系探索
- 相关性矩阵分析
- 离散变量与连续变量关系
- 统计显著性检验

### 4. 时间序列特征分析
- 平稳性检验（ADF检验）
- 自相关性分析
- 变化点检测

### 5. 降维分析与模式识别
- 主成分分析（PCA）
- 主成分载荷分析
- 聚类分析

### 6. 机器学习模型与预测分析
- 离散变量预测模型（分类）
- 时间序列预测模型（回归）
- 特征重要性分析

### 7. 因果关系分析
- 格兰杰因果检验
- 交叉相关分析

### 8. 综合分析与洞察总结
- 关键发现汇总
- 结论与建议

## 关键技术特点

### 统计方法
- ADF平稳性检验
- 方差分析（ANOVA）
- 格兰杰因果检验
- 交叉相关分析

### 机器学习技术
- 主成分分析（PCA）
- K-means聚类
- 随机森林（分类和回归）
- 时间序列特征工程

### 可视化技术
- 时间序列图
- 相关性热力图
- 分布直方图
- 主成分载荷图
- 预测结果对比图

## 注意事项

1. **数据路径**: 确保数据文件路径正确
2. **内存使用**: 高维数据分析可能需要较大内存
3. **运行时间**: 完整分析可能需要几分钟时间
4. **图表显示**: 确保所有图表在HTML中正确显示
5. **中文字体**: 如果图表中文显示异常，可能需要安装中文字体

## 故障排除

### 常见问题

1. **导入错误**: 
   ```bash
   pip install --upgrade [package_name]
   ```

2. **数据文件未找到**:
   - 检查文件路径
   - 确认文件名拼写

3. **内存不足**:
   - 减少分析的变量数量
   - 使用数据采样

4. **图表不显示**:
   - 重新运行相关单元格
   - 检查matplotlib后端设置

### 性能优化建议

1. 如果数据量很大，可以考虑：
   - 使用数据采样进行初步分析
   - 分批处理某些计算密集型操作
   - 优化内存使用

2. 对于长时间运行的分析：
   - 保存中间结果
   - 使用进度条显示处理进度

## 提交要求

最终提交文件应为：`Cui_Wei.html`

确保HTML文件包含：
- 完整的代码
- 所有分析结果
- 清晰的可视化图表
- 详细的中文注释和说明

## 联系信息

如有技术问题，请检查：
1. Python环境和库版本
2. 数据文件完整性
3. 系统内存和存储空间

---

**重要提醒**: 请在提交前仔细检查HTML文件的完整性和可读性，确保所有分析内容都正确显示。
