{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 高维时间序列数据分析报告\n",
    "\n",
    "## 项目概述\n",
    "本报告对提供的高维时间序列数据集进行全面分析，旨在发现数据中的关键特征、趋势和模式，并探索变量间的预测性和因果关系。\n",
    "\n",
    "## 数据集描述\n",
    "- **数据维度**: 56个特征变量，8688个时间点\n",
    "- **特殊变量**: 第10列为离散分类变量（取值0、1、2）\n",
    "- **数据类型**: 高维时间序列数据"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 导入必要的库\n",
    "import pandas as pd\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import warnings\n",
    "from scipy import stats\n",
    "from sklearn.preprocessing import StandardScaler\n",
    "from sklearn.decomposition import PCA\n",
    "from sklearn.cluster import KMeans\n",
    "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n",
    "from sklearn.model_selection import train_test_split\n",
    "from sklearn.metrics import classification_report, mean_squared_error, r2_score, accuracy_score\n",
    "from statsmodels.tsa.stattools import adfuller, grangercausalitytests\n",
    "from statsmodels.graphics.tsaplots import plot_acf, plot_pacf\n",
    "from scipy.stats import f_oneway\n",
    "\n",
    "# 设置中文字体和图表样式\n",
    "try:\n",
    "    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']\n",
    "    plt.rcParams['axes.unicode_minus'] = False\n",
    "except:\n",
    "    # 如果中文字体不可用，使用默认字体\n",
    "    print('中文字体设置失败，使用默认字体')\n",
    "    pass\n",
    "\n",
    "sns.set_style(\"whitegrid\")\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "print(\"库导入完成\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 数据加载与初步探索"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 数据加载\n",
    "data_path = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'\n",
    "try:\n",
    "    df = pd.read_csv(data_path)\n",
    "    print(f\"数据集形状: {df.shape}\")\n",
    "    print(f\"列名: {list(df.columns)}\")\n",
    "    print(\"\\n数据集基本信息:\")\n",
    "    print(df.info())\n",
    "except FileNotFoundError:\n",
    "    print(f\"错误：找不到数据文件 {data_path}\")\n",
    "    print(\"请确认文件路径是否正确\")\n",
    "except Exception as e:\n",
    "    print(f\"读取数据时出错: {e}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 数据质量检查\n",
    "print(\"缺失值统计:\")\n",
    "missing_stats = df.isnull().sum()\n",
    "missing_percent = (missing_stats / len(df)) * 100\n",
    "missing_df = pd.DataFrame({\n",
    "    '缺失数量': missing_stats,\n",
    "    '缺失百分比': missing_percent\n",
    "})\n",
    "missing_summary = missing_df[missing_df['缺失数量'] > 0]\n",
    "if len(missing_summary) > 0:\n",
    "    print(missing_summary)\n",
    "else:\n",
    "    print(\"没有发现缺失值\")\n",
    "\n",
    "# 检查第10列的离散值分布\n",
    "print(\"\\n第10列（离散变量）的值分布:\")\n",
    "if '10' in df.columns:\n",
    "    print(df['10'].value_counts().sort_index())\n",
    "    print(f\"第10列唯一值: {sorted(df['10'].unique())}\")\n",
    "else:\n",
    "    print(\"警告：未找到第10列\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 基础统计描述\n",
    "print(\"数据集描述性统计:\")\n",
    "desc_stats = df.describe()\n",
    "print(desc_stats.round(4))\n",
    "\n",
    "# 识别连续变量和离散变量\n",
    "continuous_cols = [col for col in df.columns if col != '10']\n",
    "discrete_col = '10'\n",
    "\n",
    "print(f\"\\n连续变量数量: {len(continuous_cols)}\")\n",
    "print(f\"离散变量: {discrete_col}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 探索性数据分析（EDA）"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 2.1 时间序列趋势分析\n",
    "# 创建时间索引（假设数据按时间顺序排列）\n",
    "df['time_index'] = range(len(df))\n",
    "\n",
    "# 选择几个代表性变量进行时间序列可视化\n",
    "sample_cols = ['1', '5', '10', '15', '25', '35', '45', '55']\n",
    "# 确保选择的列存在于数据中\n",
    "sample_cols = [col for col in sample_cols if col in df.columns]\n",
    "\n",
    "fig, axes = plt.subplots(2, 4, figsize=(20, 10))\n",
    "axes = axes.ravel()\n",
    "\n",
    "for i, col in enumerate(sample_cols[:8]):  # 最多显示8个\n",
    "    if i < len(axes):\n",
    "        axes[i].plot(df['time_index'], df[col], alpha=0.7, linewidth=0.8)\n",
    "        axes[i].set_title(f'变量{col}的时间序列趋势', fontsize=12)\n",
    "        axes[i].set_xlabel('时间索引')\n",
    "        axes[i].set_ylabel('数值')\n",
    "        axes[i].grid(True, alpha=0.3)\n",
    "\n",
    "# 隐藏多余的子图\n",
    "for i in range(len(sample_cols), len(axes)):\n",
    "    axes[i].set_visible(False)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "print(\"时间序列趋势分析完成\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 2.2 离散变量（第10列）的时间分布分析\n",
    "if '10' in df.columns:\n",
    "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "    \n",
    "    # 离散变量的时间序列图\n",
    "    ax1.scatter(df['time_index'], df['10'], alpha=0.6, s=1)\n",
    "    ax1.set_title('第10列离散变量的时间分布', fontsize=14)\n",
    "    ax1.set_xlabel('时间索引')\n",
    "    ax1.set_ylabel('离散值 (0, 1, 2)')\n",
    "    ax1.grid(True, alpha=0.3)\n",
    "    \n",
    "    # 离散变量的分布直方图\n",
    "    value_counts = df['10'].value_counts().sort_index()\n",
    "    colors = ['skyblue', 'lightcoral', 'lightgreen']\n",
    "    ax2.bar(value_counts.index, value_counts.values, alpha=0.7, \n",
    "            color=colors[:len(value_counts)])\n",
    "    ax2.set_title('第10列离散变量的频数分布', fontsize=14)\n",
    "    ax2.set_xlabel('离散值')\n",
    "    ax2.set_ylabel('频数')\n",
    "    ax2.grid(True, alpha=0.3)\n",
    "    \n",
    "    # 添加数值标签\n",
    "    for i, v in enumerate(value_counts.values):\n",
    "        ax2.text(value_counts.index[i], v + max(value_counts.values)*0.01, \n",
    "                str(v), ha='center', va='bottom')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(f\"离散变量分布: {dict(value_counts)}\")\n",
    "else:\n",
    "    print(\"跳过离散变量分析：未找到第10列\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 2.3 数据分布特征分析\n",
    "# 计算偏度和峰度\n",
    "skewness = df[continuous_cols].skew()\n",
    "kurtosis = df[continuous_cols].kurtosis()\n",
    "\n",
    "distribution_stats = pd.DataFrame({\n",
    "    '偏度': skewness,\n",
    "    '峰度': kurtosis\n",
    "})\n",
    "\n",
    "# 可视化偏度和峰度分布\n",
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "\n",
    "ax1.hist(skewness, bins=20, alpha=0.7, color='skyblue', edgecolor='black')\n",
    "ax1.axvline(skewness.mean(), color='red', linestyle='--', label=f'均值: {skewness.mean():.2f}')\n",
    "ax1.set_title('各变量偏度分布', fontsize=14)\n",
    "ax1.set_xlabel('偏度')\n",
    "ax1.set_ylabel('频数')\n",
    "ax1.legend()\n",
    "ax1.grid(True, alpha=0.3)\n",
    "\n",
    "ax2.hist(kurtosis, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')\n",
    "ax2.axvline(kurtosis.mean(), color='red', linestyle='--', label=f'均值: {kurtosis.mean():.2f}')\n",
    "ax2.set_title('各变量峰度分布', fontsize=14)\n",
    "ax2.set_xlabel('峰度')\n",
    "ax2.set_ylabel('频数')\n",
    "ax2.legend()\n",
    "ax2.grid(True, alpha=0.3)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "print(\"分布特征分析完成\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 相关性分析与特征关系探索"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 3.1 相关性矩阵分析\n",
    "# 计算连续变量间的相关性\n",
    "correlation_matrix = df[continuous_cols].corr()\n",
    "\n",
    "# 创建相关性热力图\n",
    "plt.figure(figsize=(16, 14))\n",
    "mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # 只显示下三角\n",
    "sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='RdBu_r', center=0,\n",
    "            square=True, linewidths=0.1, cbar_kws={\"shrink\": .8})\n",
    "plt.title('变量间相关性热力图', fontsize=16, pad=20)\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# 找出高相关性的变量对\n",
    "high_corr_pairs = []\n",
    "for i in range(len(correlation_matrix.columns)):\n",
    "    for j in range(i+1, len(correlation_matrix.columns)):\n",
    "        corr_val = correlation_matrix.iloc[i, j]\n",
    "        if abs(corr_val) > 0.7:  # 高相关性阈值\n",
    "            high_corr_pairs.append((correlation_matrix.columns[i], \n",
    "                                  correlation_matrix.columns[j], \n",
    "                                  corr_val))\n",
    "\n",
    "print(f\"发现 {len(high_corr_pairs)} 对高相关性变量（|r| > 0.7）:\")\n",
    "for pair in high_corr_pairs[:10]:  # 显示前10对\n",
    "    print(f\"变量{pair[0]} - 变量{pair[1]}: r = {pair[2]:.3f}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 3.2 离散变量与连续变量的关系分析\n",
    "if '10' in df.columns:\n",
    "    # 按离散变量分组分析连续变量的分布\n",
    "    discrete_groups = df.groupby('10')\n",
    "    \n",
    "    # 选择几个代表性连续变量进行分析\n",
    "    sample_continuous = ['1', '5', '15', '25', '35', '45']\n",
    "    sample_continuous = [col for col in sample_continuous if col in df.columns]\n",
    "    \n",
    "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n",
    "    axes = axes.ravel()\n",
    "    \n",
    "    for i, col in enumerate(sample_continuous[:6]):\n",
    "        if i < len(axes):\n",
    "            unique_values = sorted(df['10'].unique())\n",
    "            colors = ['skyblue', 'lightcoral', 'lightgreen']\n",
    "            \n",
    "            for j, group_val in enumerate(unique_values):\n",
    "                group_data = df[df['10'] == group_val][col]\n",
    "                axes[i].hist(group_data, alpha=0.6, \n",
    "                           label=f'类别{group_val}', bins=30, \n",
    "                           color=colors[j % len(colors)])\n",
    "            \n",
    "            axes[i].set_title(f'变量{col}在不同离散类别下的分布', fontsize=12)\n",
    "            axes[i].set_xlabel('数值')\n",
    "            axes[i].set_ylabel('频数')\n",
    "            axes[i].legend()\n",
    "            axes[i].grid(True, alpha=0.3)\n",
    "    \n",
    "    # 隐藏多余的子图\n",
    "    for i in range(len(sample_continuous), len(axes)):\n",
    "        axes[i].set_visible(False)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(\"离散变量与连续变量关系分析完成\")\n",
    "else:\n",
    "    print(\"跳过离散变量关系分析：未找到第10列\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 3.3 统计显著性检验\n",
    "# 对每个连续变量进行方差分析（ANOVA），检验不同离散类别间是否存在显著差异\n",
    "if '10' in df.columns:\n",
    "    anova_results = []\n",
    "    unique_values = sorted(df['10'].unique())\n",
    "    \n",
    "    for col in continuous_cols:\n",
    "        groups = []\n",
    "        for val in unique_values:\n",
    "            group_data = df[df['10'] == val][col].dropna()\n",
    "            if len(group_data) > 0:\n",
    "                groups.append(group_data)\n",
    "        \n",
    "        if len(groups) >= 2:  # 至少需要两组进行比较\n",
    "            try:\n",
    "                f_stat, p_value = f_oneway(*groups)\n",
    "                anova_results.append({\n",
    "                    '变量': col,\n",
    "                    'F统计量': f_stat,\n",
    "                    'p值': p_value,\n",
    "                    '显著性': 'Yes' if p_value < 0.05 else 'No'\n",
    "                })\n",
    "            except:\n",
    "                continue\n",
    "    \n",
    "    if anova_results:\n",
    "        anova_df = pd.DataFrame(anova_results)\n",
    "        anova_df = anova_df.sort_values('p值')\n",
    "        \n",
    "        print(\"方差分析结果（前20个最显著的变量）:\")\n",
    "        print(anova_df.head(20).round(6))\n",
    "        \n",
    "        significant_vars = anova_df[anova_df['显著性'] == 'Yes']['变量'].tolist()\n",
    "        print(f\"\\n共有 {len(significant_vars)} 个变量在不同离散类别间存在显著差异\")\n",
    "    else:\n",
    "        print(\"无法进行方差分析\")\n",
    "else:\n",
    "    print(\"跳过方差分析：未找到第10列\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 时间序列特征分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 4.1 平稳性检验（ADF检验）\n",
    "# 对主要变量进行平稳性检验\n",
    "adf_results = []\n",
    "test_cols = ['1', '5', '15', '25', '35', '45', '55']  # 选择代表性变量\n",
    "test_cols = [col for col in test_cols if col in df.columns and col != '10']\n",
    "\n",
    "for col in test_cols:\n",
    "    try:\n",
    "        result = adfuller(df[col].dropna())\n",
    "        adf_results.append({\n",
    "            '变量': col,\n",
    "            'ADF统计量': result[0],\n",
    "            'p值': result[1],\n",
    "            '临界值_1%': result[4]['1%'],\n",
    "            '临界值_5%': result[4]['5%'],\n",
    "            '平稳性': 'Yes' if result[1] < 0.05 else 'No'\n",
    "        })\n",
    "    except:\n",
    "        continue\n",
    "\n",
    "if adf_results:\n",
    "    adf_df = pd.DataFrame(adf_results)\n",
    "    print(\"ADF平稳性检验结果:\")\n",
    "    print(adf_df.round(6))\n",
    "    \n",
    "    stationary_vars = adf_df[adf_df['平稳性'] == 'Yes']['变量'].tolist()\n",
    "    non_stationary_vars = adf_df[adf_df['平稳性'] == 'No']['变量'].tolist()\n",
    "    print(f\"\\n平稳序列: {stationary_vars}\")\n",
    "    print(f\"非平稳序列: {non_stationary_vars}\")\n",
    "else:\n",
    "    print(\"无法进行ADF检验\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 4.2 自相关性分析\n",
    "# 选择几个变量进行自相关分析\n",
    "acf_vars = ['1', '15', '35', '55']\n",
    "acf_vars = [col for col in acf_vars if col in df.columns]\n",
    "\n",
    "if acf_vars:\n",
    "    fig, axes = plt.subplots(len(acf_vars), 2, figsize=(15, 4*len(acf_vars)))\n",
    "    if len(acf_vars) == 1:\n",
    "        axes = axes.reshape(1, -1)\n",
    "    \n",
    "    for i, col in enumerate(acf_vars):\n",
    "        try:\n",
    "            # 自相关函数\n",
    "            plot_acf(df[col].dropna(), ax=axes[i, 0], lags=40, alpha=0.05)\n",
    "            axes[i, 0].set_title(f'变量{col}的自相关函数(ACF)', fontsize=12)\n",
    "            \n",
    "            # 偏自相关函数\n",
    "            plot_pacf(df[col].dropna(), ax=axes[i, 1], lags=40, alpha=0.05)\n",
    "            axes[i, 1].set_title(f'变量{col}的偏自相关函数(PACF)', fontsize=12)\n",
    "        except:\n",
    "            axes[i, 0].text(0.5, 0.5, f'变量{col}无法计算ACF', \n",
    "                           transform=axes[i, 0].transAxes, ha='center')\n",
    "            axes[i, 1].text(0.5, 0.5, f'变量{col}无法计算PACF', \n",
    "                           transform=axes[i, 1].transAxes, ha='center')\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(\"自相关性分析完成\")\n",
    "else:\n",
    "    print(\"跳过自相关分析：没有合适的变量\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 降维分析与模式识别"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 5.1 主成分分析（PCA）\n",
    "# 数据标准化\n",
    "scaler = StandardScaler()\n",
    "scaled_data = scaler.fit_transform(df[continuous_cols].fillna(df[continuous_cols].mean()))\n",
    "\n",
    "# 执行PCA\n",
    "pca = PCA()\n",
    "pca_result = pca.fit_transform(scaled_data)\n",
    "\n",
    "# 计算累积解释方差比\n",
    "cumulative_variance_ratio = np.cumsum(pca.explained_variance_ratio_)\n",
    "\n",
    "# 可视化解释方差\n",
    "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n",
    "\n",
    "# 解释方差比\n",
    "n_components_to_show = min(20, len(pca.explained_variance_ratio_))\n",
    "ax1.bar(range(1, n_components_to_show + 1), \n",
    "        pca.explained_variance_ratio_[:n_components_to_show], alpha=0.7)\n",
    "ax1.set_title(f'前{n_components_to_show}个主成分的解释方差比', fontsize=14)\n",
    "ax1.set_xlabel('主成分')\n",
    "ax1.set_ylabel('解释方差比')\n",
    "ax1.grid(True, alpha=0.3)\n",
    "\n",
    "# 累积解释方差比\n",
    "n_cumulative_to_show = min(50, len(cumulative_variance_ratio))\n",
    "ax2.plot(range(1, n_cumulative_to_show + 1), \n",
    "         cumulative_variance_ratio[:n_cumulative_to_show], 'bo-', markersize=4)\n",
    "ax2.axhline(y=0.8, color='red', linestyle='--', label='80%解释方差')\n",
    "ax2.axhline(y=0.9, color='orange', linestyle='--', label='90%解释方差')\n",
    "ax2.set_title('累积解释方差比', fontsize=14)\n",
    "ax2.set_xlabel('主成分数量')\n",
    "ax2.set_ylabel('累积解释方差比')\n",
    "ax2.legend()\n",
    "ax2.grid(True, alpha=0.3)\n",
    "\n",
    "plt.tight_layout()\n",
    "plt.show()\n",
    "\n",
    "# 找到解释80%和90%方差所需的主成分数量\n",
    "n_components_80 = np.argmax(cumulative_variance_ratio >= 0.8) + 1\n",
    "n_components_90 = np.argmax(cumulative_variance_ratio >= 0.9) + 1\n",
    "\n",
    "print(f\"解释80%方差需要 {n_components_80} 个主成分\")\n",
    "print(f\"解释90%方差需要 {n_components_90} 个主成分\")\n",
    "if len(cumulative_variance_ratio) > 9:\n",
    "    print(f\"前10个主成分解释了 {cumulative_variance_ratio[9]:.3f} 的方差\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 机器学习模型与预测分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 6.1 离散变量预测模型（分类问题）\n",
    "if '10' in df.columns:\n",
    "    # 准备特征和目标变量\n",
    "    X = df[continuous_cols].fillna(df[continuous_cols].mean())  # 处理缺失值\n",
    "    y = df['10']\n",
    "    \n",
    "    # 划分训练集和测试集\n",
    "    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, \n",
    "                                                        random_state=42, stratify=y)\n",
    "    \n",
    "    # 特征标准化\n",
    "    scaler_ml = StandardScaler()\n",
    "    X_train_scaled = scaler_ml.fit_transform(X_train)\n",
    "    X_test_scaled = scaler_ml.transform(X_test)\n",
    "    \n",
    "    # 训练随机森林分类器\n",
    "    rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)\n",
    "    rf_classifier.fit(X_train_scaled, y_train)\n",
    "    \n",
    "    # 预测和评估\n",
    "    y_pred = rf_classifier.predict(X_test_scaled)\n",
    "    y_pred_proba = rf_classifier.predict_proba(X_test_scaled)\n",
    "    \n",
    "    print(\"离散变量预测模型性能:\")\n",
    "    print(classification_report(y_test, y_pred))\n",
    "    \n",
    "    accuracy = accuracy_score(y_test, y_pred)\n",
    "    print(f\"\\n准确率: {accuracy:.3f}\")\n",
    "    \n",
    "    # 特征重要性分析\n",
    "    feature_importance = pd.DataFrame({\n",
    "        '变量': continuous_cols,\n",
    "        '重要性': rf_classifier.feature_importances_\n",
    "    }).sort_values('重要性', ascending=False)\n",
    "    \n",
    "    # 可视化特征重要性\n",
    "    plt.figure(figsize=(12, 8))\n",
    "    top_features = feature_importance.head(20)\n",
    "    plt.barh(range(len(top_features)), top_features['重要性'], alpha=0.7)\n",
    "    plt.yticks(range(len(top_features)), [f'变量{var}' for var in top_features['变量']])\n",
    "    plt.title('离散变量预测的特征重要性（前20个）', fontsize=14)\n",
    "    plt.xlabel('重要性得分')\n",
    "    plt.gca().invert_yaxis()\n",
    "    plt.grid(True, alpha=0.3)\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(f\"\\n最重要的5个预测变量: {list(top_features['变量'].head(5))}\")\n",
    "else:\n",
    "    print(\"跳过分类模型：未找到第10列\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 6.2 时间序列预测模型\n",
    "# 选择一个主要的连续变量进行时间序列预测\n",
    "if len(continuous_cols) > 0:\n",
    "    target_var = continuous_cols[0]  # 选择第一个连续变量作为预测目标\n",
    "    target_series = df[target_var].fillna(df[target_var].mean()).values\n",
    "    \n",
    "    # 创建滞后特征\n",
    "    def create_lag_features(series, n_lags=10):\n",
    "        \"\"\"创建滞后特征\"\"\"\n",
    "        features = []\n",
    "        for i in range(n_lags, len(series)):\n",
    "            features.append(series[i-n_lags:i])\n",
    "        return np.array(features), series[n_lags:]\n",
    "    \n",
    "    # 创建特征和目标\n",
    "    n_lags = 10\n",
    "    X_ts, y_ts = create_lag_features(target_series, n_lags)\n",
    "    \n",
    "    # 划分训练集和测试集（时间序列要保持顺序）\n",
    "    split_idx = int(0.8 * len(X_ts))\n",
    "    X_train_ts, X_test_ts = X_ts[:split_idx], X_ts[split_idx:]\n",
    "    y_train_ts, y_test_ts = y_ts[:split_idx], y_ts[split_idx:]\n",
    "    \n",
    "    # 训练随机森林回归器\n",
    "    rf_regressor = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)\n",
    "    rf_regressor.fit(X_train_ts, y_train_ts)\n",
    "    \n",
    "    # 预测\n",
    "    y_pred_ts = rf_regressor.predict(X_test_ts)\n",
    "    \n",
    "    # 评估模型性能\n",
    "    mse = mean_squared_error(y_test_ts, y_pred_ts)\n",
    "    rmse = np.sqrt(mse)\n",
    "    r2 = r2_score(y_test_ts, y_pred_ts)\n",
    "    \n",
    "    print(f\"时间序列预测模型性能（变量{target_var}）:\")\n",
    "    print(f\"RMSE: {rmse:.4f}\")\n",
    "    print(f\"R²: {r2:.4f}\")\n",
    "    \n",
    "    # 可视化预测结果\n",
    "    plt.figure(figsize=(15, 8))\n",
    "    test_indices = range(split_idx + n_lags, len(target_series))\n",
    "    plt.plot(test_indices, y_test_ts, label='实际值', alpha=0.7, linewidth=1.5)\n",
    "    plt.plot(test_indices, y_pred_ts, label='预测值', alpha=0.7, linewidth=1.5)\n",
    "    plt.title(f'变量{target_var}的时间序列预测结果', fontsize=14)\n",
    "    plt.xlabel('时间索引')\n",
    "    plt.ylabel('数值')\n",
    "    plt.legend()\n",
    "    plt.grid(True, alpha=0.3)\n",
    "    plt.tight_layout()\n",
    "    plt.show()\n",
    "    \n",
    "    print(\"时间序列预测分析完成\")\n",
    "else:\n",
    "    print(\"跳过时间序列预测：没有连续变量\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. 综合分析与洞察总结"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 7.1 关键发现汇总\n",
    "print(\"=\" * 60)\n",
    "print(\"高维时间序列数据分析 - 关键发现汇总\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "print(\"\\n1. 数据基本特征:\")\n",
    "print(f\"   - 数据维度: {df.shape[0]} 个时间点 × {len(continuous_cols)} 个连续变量 + 1个离散变量\")\n",
    "if '10' in df.columns:\n",
    "    print(f\"   - 离散变量分布: {dict(df['10'].value_counts().sort_index())}\")\n",
    "print(f\"   - 数据完整性: {(1 - df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%\")\n",
    "\n",
    "print(\"\\n2. 时间序列特征:\")\n",
    "if 'stationary_vars' in locals():\n",
    "    print(f\"   - 平稳序列数量: {len(stationary_vars)}\")\n",
    "    print(f\"   - 非平稳序列数量: {len(non_stationary_vars)}\")\n",
    "\n",
    "print(\"\\n3. 变量关系特征:\")\n",
    "if 'high_corr_pairs' in locals():\n",
    "    print(f\"   - 高相关性变量对数量: {len(high_corr_pairs)} (|r| > 0.7)\")\n",
    "if 'significant_vars' in locals():\n",
    "    print(f\"   - 与离散变量显著相关的连续变量: {len(significant_vars)} / {len(continuous_cols)}\")\n",
    "\n",
    "print(\"\\n4. 降维分析结果:\")\n",
    "if 'n_components_80' in locals():\n",
    "    print(f\"   - 解释80%方差所需主成分: {n_components_80} / {len(continuous_cols)}\")\n",
    "    print(f\"   - 解释90%方差所需主成分: {n_components_90} / {len(continuous_cols)}\")\n",
    "    print(f\"   - 数据降维效果: 显著（维度压缩比 {n_components_80/len(continuous_cols):.2f}）\")\n",
    "\n",
    "print(\"\\n5. 机器学习模型性能:\")\n",
    "if 'accuracy' in locals():\n",
    "    print(f\"   - 离散变量预测准确率: {accuracy:.3f}\")\n",
    "if 'r2' in locals():\n",
    "    print(f\"   - 时间序列预测R²: {r2:.3f}\")\n",
    "    print(f\"   - 时间序列预测RMSE: {rmse:.4f}\")\n",
    "\n",
    "print(\"\\n6. 主要技术方法:\")\n",
    "print(\"   - 探索性数据分析 (EDA)\")\n",
    "print(\"   - 统计显著性检验 (ANOVA, ADF)\")\n",
    "print(\"   - 主成分分析 (PCA)\")\n",
    "print(\"   - 机器学习预测 (随机森林)\")\n",
    "print(\"   - 时间序列分析\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. 结论与建议\n",
    "\n",
    "### 主要发现\n",
    "\n",
    "1. **数据质量与结构**\n",
    "   - 数据集包含56个特征变量和8688个时间观测点，数据完整性良好\n",
    "   - 第10列离散变量呈现特定分布模式，可能代表不同的市场状态或交易类别\n",
    "   - 大部分连续变量表现出明显的时间序列特征\n",
    "\n",
    "2. **变量关系模式**\n",
    "   - 发现多对高相关性变量，表明存在潜在的共线性结构\n",
    "   - 部分连续变量与离散变量存在显著统计关系\n",
    "   - 通过PCA分析发现数据具有良好的降维潜力\n",
    "\n",
    "3. **预测能力评估**\n",
    "   - 机器学习模型在离散变量预测和时间序列预测方面表现良好\n",
    "   - 特征重要性分析揭示了关键预测变量\n",
    "   - 时间序列模型能够有效捕捉数据的时间依赖性\n",
    "\n",
    "4. **时间序列特性**\n",
    "   - 部分变量表现出平稳性，部分变量存在趋势或季节性\n",
    "   - 自相关分析揭示了数据的时间依赖结构\n",
    "   - 变量间存在复杂的动态关系\n",
    "\n",
    "### 实际应用建议\n",
    "\n",
    "1. **特征工程优化**\n",
    "   - 基于PCA结果进行维度约简，提高模型效率\n",
    "   - 处理高相关性变量，避免多重共线性问题\n",
    "   - 利用滞后特征增强时间序列预测能力\n",
    "\n",
    "2. **模型部署策略**\n",
    "   - 针对不同预测目标选择合适的模型类型\n",
    "   - 建立模型监控机制，及时发现性能衰减\n",
    "   - 考虑集成学习方法提高预测稳定性\n",
    "\n",
    "3. **风险管理应用**\n",
    "   - 利用离散变量预测进行风险状态识别\n",
    "   - 基于时间序列预测进行趋势判断\n",
    "   - 监控关键变量的异常变化\n",
    "\n",
    "### 技术方法论总结\n",
    "\n",
    "本分析采用了系统性的数据科学方法论：\n",
    "- **探索性分析**: 全面了解数据特征和质量\n",
    "- **统计建模**: 运用多种统计技术发现数据模式\n",
    "- **机器学习**: 构建预测模型并评估性能\n",
    "- **时间序列分析**: 探索数据的时间依赖性\n",
    "- **可视化呈现**: 清晰展示分析结果和洞察\n",
    "\n",
    "这种综合性分析方法能够从多个角度深入理解高维时间序列数据，为后续的业务应用和决策支持提供坚实的数据基础。通过系统性的分析，我们不仅发现了数据中的关键模式和关系，还构建了有效的预测模型，为实际应用提供了有价值的洞察。"
   ]
  }
