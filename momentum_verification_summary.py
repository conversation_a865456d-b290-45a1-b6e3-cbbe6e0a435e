#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股与转债动量效应验证 - 最终总结报告
"""

import pandas as pd
import numpy as np
import os

def generate_summary_report():
    """生成总结报告"""
    
    print("=" * 80)
    print("A股与转债动量效应验证分析 - 最终报告")
    print("=" * 80)
    
    # 读取结果数据
    results_path = '/Users/<USER>/Desktop/动量验证/动量策略择时结果.xlsx'
    results_df = pd.read_excel(results_path, index_col=0)
    
    print("\n📊 动量策略绩效对比:")
    print("-" * 60)
    print(results_df)
    
    print("\n🎯 验证结论:")
    print("-" * 60)
    
    # 提取夏普比率进行比较
    sharpe_ratios = {}
    for index in results_df.index:
        sharpe_ratios[index] = float(results_df.loc[index, '夏普比率'])
    
    convertible_bond_sharpe = sharpe_ratios['万得等权重指数']
    stock_indices = ['国证2000', '转债对应正股指数']
    
    print(f"万得等权重指数（转债市场）夏普比率: {convertible_bond_sharpe:.2f}")
    
    verification_success = True
    for stock_index in stock_indices:
        stock_sharpe = sharpe_ratios[stock_index]
        print(f"{stock_index}（A股市场）夏普比率: {stock_sharpe:.2f}")
        
        if convertible_bond_sharpe > stock_sharpe:
            print(f"✅ {stock_index}的动量效应确实弱于转债市场")
        else:
            print(f"❌ {stock_index}的动量效应强于转债市场")
            verification_success = False
    
    print(f"\n🏆 总体验证结果: {'成功' if verification_success else '失败'}")
    
    if verification_success:
        print("✅ 验证成功：A股市场的动量效应确实不如转债市场的动量效应强")
    else:
        print("❌ 验证失败：A股市场的动量效应强于转债市场")
    
    print("\n📈 关键发现:")
    print("-" * 60)
    
    # 按夏普比率排序
    sorted_indices = sorted(sharpe_ratios.items(), key=lambda x: x[1], reverse=True)
    
    print("动量效应强弱排序（按夏普比率）:")
    for i, (name, sharpe) in enumerate(sorted_indices, 1):
        annual_return = results_df.loc[name, '年化收益率']
        max_drawdown = results_df.loc[name, '最大回撤']
        print(f"{i}. {name}: 夏普比率 {sharpe:.2f}, 年化收益率 {annual_return}, 最大回撤 {max_drawdown}")
    
    print(f"\n🔍 深度分析:")
    print("-" * 60)
    print("1. 万得等权重指数（转债）表现最优：")
    print(f"   - 夏普比率最高: {convertible_bond_sharpe:.2f}")
    print(f"   - 年化收益率: {results_df.loc['万得等权重指数', '年化收益率']}")
    print(f"   - 最大回撤最小: {results_df.loc['万得等权重指数', '最大回撤']}")
    
    print("\n2. A股指数表现相对较弱：")
    for stock_index in stock_indices:
        print(f"   - {stock_index}: 夏普比率 {sharpe_ratios[stock_index]:.2f}")
        print(f"     年化收益率 {results_df.loc[stock_index, '年化收益率']}, 最大回撤 {results_df.loc[stock_index, '最大回撤']}")
    
    print("\n3. 可能原因分析：")
    print("   - 转债市场具有债券属性，波动性相对较小")
    print("   - 转债市场流动性特征可能更适合动量策略")
    print("   - A股市场噪音较大，动量信号容易被干扰")
    print("   - 转债市场参与者结构可能导致更强的趋势持续性")
    
    print("\n📁 生成文件清单:")
    print("-" * 60)
    output_dir = '/Users/<USER>/Desktop/动量验证'
    files = os.listdir(output_dir)
    for i, file in enumerate(sorted(files), 1):
        print(f"{i}. {file}")
    
    print("\n📋 策略参数:")
    print("-" * 60)
    print("- 动量计算期间: 20个交易日")
    print("- 信号生成规则: 动量为正时买入，为负时空仓")
    print("- 数据时间范围: 2018-01-02 至 2025-08-11")
    print("- 总交易日数: 1,846天")
    
    print("\n💡 投资建议:")
    print("-" * 60)
    print("1. 转债市场的动量策略效果显著优于A股市场")
    print("2. 建议在转债市场应用动量择时策略")
    print("3. A股市场可考虑其他因子或策略组合")
    print("4. 风险控制方面，转债策略的回撤控制更好")
    
    print("\n" + "=" * 80)
    print("分析完成！所有结果已保存至 /Users/<USER>/Desktop/动量验证/ 文件夹")
    print("=" * 80)

if __name__ == "__main__":
    generate_summary_report()
