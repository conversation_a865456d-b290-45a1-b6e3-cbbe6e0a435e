#!/usr/bin/env python3
"""
测试脚本 - 验证分析流程
"""

import os
import sys
import subprocess

def test_data_file():
    """测试数据文件"""
    data_path = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'
    
    if not os.path.exists(data_path):
        print(f"数据文件不存在: {data_path}")
        return False
    
    try:
        import pandas as pd
        df = pd.read_csv(data_path)
        print(f"数据文件检查通过:")
        print(f"  - 形状: {df.shape}")
        print(f"  - 列数: {len(df.columns)}")
        print(f"  - 第10列存在: {'10' in df.columns}")
        if '10' in df.columns:
            print(f"  - 第10列唯一值: {sorted(df['10'].unique())}")
        return True
    except Exception as e:
        print(f"数据文件读取失败: {e}")
        return False

def test_notebook_file():
    """测试notebook文件"""
    notebook_path = '时间序列数据分析.ipynb'
    
    if not os.path.exists(notebook_path):
        print(f"Notebook文件不存在: {notebook_path}")
        return False
    
    try:
        import json
        with open(notebook_path, 'r', encoding='utf-8') as f:
            nb = json.load(f)
        
        cell_count = len(nb['cells'])
        code_cells = len([c for c in nb['cells'] if c['cell_type'] == 'code'])
        
        print(f"Notebook文件检查通过:")
        print(f"  - 总单元格数: {cell_count}")
        print(f"  - 代码单元格数: {code_cells}")
        return True
    except Exception as e:
        print(f"Notebook文件检查失败: {e}")
        return False

def test_packages():
    """测试必要的包"""
    packages = ['pandas', 'numpy', 'matplotlib', 'seaborn', 'sklearn', 'scipy', 'statsmodels']
    
    missing = []
    for pkg in packages:
        try:
            if pkg == 'sklearn':
                __import__('sklearn')
            else:
                __import__(pkg)
        except ImportError:
            missing.append(pkg)
    
    if missing:
        print(f"缺失的包: {missing}")
        return False
    else:
        print("所有必要的包都已安装")
        return True

def test_jupyter():
    """测试jupyter"""
    try:
        result = subprocess.run([sys.executable, '-m', 'jupyter', '--version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("Jupyter可用")
            return True
        else:
            print("Jupyter不可用")
            return False
    except:
        print("Jupyter未安装")
        return False

def run_quick_test():
    """运行快速测试"""
    print("快速测试分析流程...")
    
    try:
        # 创建简单的测试notebook
        test_nb = {
            "cells": [
                {
                    "cell_type": "code",
                    "execution_count": None,
                    "metadata": {},
                    "outputs": [],
                    "source": [
                        "import pandas as pd\n",
                        "import numpy as np\n",
                        "print('测试成功')\n",
                        "df = pd.DataFrame({'A': [1,2,3], 'B': [4,5,6]})\n",
                        "print(df)"
                    ]
                }
            ],
            "metadata": {
                "kernelspec": {
                    "display_name": "Python 3",
                    "language": "python",
                    "name": "python3"
                }
            },
            "nbformat": 4,
            "nbformat_minor": 4
        }
        
        # 保存测试notebook
        import json
        with open('test.ipynb', 'w') as f:
            json.dump(test_nb, f)
        
        # 运行测试
        cmd = [sys.executable, '-m', 'jupyter', 'nbconvert', 
               '--to', 'html', '--execute', 'test.ipynb']
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        # 清理
        if os.path.exists('test.ipynb'):
            os.remove('test.ipynb')
        if os.path.exists('test.html'):
            os.remove('test.html')
        
        if result.returncode == 0:
            print("Jupyter执行测试通过")
            return True
        else:
            print(f"Jupyter执行测试失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"快速测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("环境测试")
    print("=" * 50)
    
    tests = [
        ("数据文件", test_data_file),
        ("Notebook文件", test_notebook_file),
        ("Python包", test_packages),
        ("Jupyter", test_jupyter),
        ("执行测试", run_quick_test)
    ]
    
    results = []
    for name, test_func in tests:
        print(f"\\n{name}测试:")
        result = test_func()
        results.append((name, result))
        print(f"结果: {'通过' if result else '失败'}")
    
    print("\\n" + "=" * 50)
    print("测试总结")
    print("=" * 50)
    
    all_passed = True
    for name, result in results:
        status = "✓" if result else "✗"
        print(f"{status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\\n所有测试通过！可以运行主分析脚本")
        print("运行: python 运行分析.py")
    else:
        print("\\n部分测试失败，请解决问题后重试")

if __name__ == "__main__":
    main()
