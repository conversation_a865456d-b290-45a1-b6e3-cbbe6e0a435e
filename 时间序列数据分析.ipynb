{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 高维时间序列数据分析\n", "\n", "## 数据概述\n", "本次分析针对一个包含56个特征的高维时间序列数据集，数据包含8688个时间点。其中第10列为离散分类变量，其余为连续变量。\n", "\n", "## 分析目标\n", "- 探索数据的基本特征和分布\n", "- 识别变量间的关系和模式\n", "- 构建预测模型\n", "- 提取有价值的业务洞察"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from scipy import stats\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.decomposition import PCA\n", "from sklearn.cluster import KMeans\n", "from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.metrics import classification_report, mean_squared_error, r2_score, accuracy_score\n", "from statsmodels.tsa.stattools import adfuller\n", "from statsmodels.graphics.tsaplots import plot_acf, plot_pacf\n", "from scipy.stats import f_oneway\n", "\n", "# 设置图表样式\n", "plt.style.use('seaborn-v0_8')\n", "plt.rcParams['figure.figsize'] = (12, 8)\n", "plt.rcParams['font.size'] = 10\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"环境配置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载和基本信息"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据\n", "data_file = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'\n", "df = pd.read_csv(data_file)\n", "\n", "print(f\"数据维度: {df.shape}\")\n", "print(f\"列名: {list(df.columns)}\")\n", "print(\"\\n数据类型:\")\n", "print(df.dtypes.value_counts())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据质量检查\n", "print(\"数据完整性检查:\")\n", "missing_data = df.isnull().sum()\n", "if missing_data.sum() == 0:\n", "    print(\"✓ 无缺失值\")\n", "else:\n", "    print(f\"缺失值统计:\\n{missing_data[missing_data > 0]}\")\n", "\n", "print(\"\\n基本统计信息:\")\n", "print(df.describe().round(3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析第10列的分布\n", "print(\"第10列（分类变量）分布:\")\n", "class_dist = df['10'].value_counts().sort_index()\n", "print(class_dist)\n", "print(f\"\\n类别比例:\")\n", "print((class_dist / len(df) * 100).round(2))\n", "\n", "# 定义变量类型\n", "continuous_vars = [col for col in df.columns if col != '10']\n", "target_var = '10'\n", "print(f\"\\n连续变量数量: {len(continuous_vars)}\")\n", "print(f\"分类变量: {target_var}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据探索分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 时间序列可视化\n", "df['time_idx'] = range(len(df))\n", "sample_vars = ['1', '5', '15', '25', '35', '45']\n", "\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 10))\n", "axes = axes.flatten()\n", "\n", "for i, var in enumerate(sample_vars):\n", "    axes[i].plot(df['time_idx'], df[var], linewidth=0.8, alpha=0.8)\n", "    axes[i].set_title(f'变量 {var} 时间序列')\n", "    axes[i].set_xlabel('时间')\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分类变量的时间分布\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 时间序列散点图\n", "colors = ['blue', 'red', 'green']\n", "for i, class_val in enumerate(sorted(df['10'].unique())):\n", "    mask = df['10'] == class_val\n", "    ax1.scatter(df.loc[mask, 'time_idx'], df.loc[mask, '10'], \n", "               c=colors[i], alpha=0.6, s=1, label=f'类别 {class_val}')\n", "\n", "ax1.set_title('分类变量时间分布')\n", "ax1.set_xlabel('时间')\n", "ax1.set_ylabel('类别')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 类别分布柱状图\n", "class_counts = df['10'].value_counts().sort_index()\n", "bars = ax2.bar(class_counts.index, class_counts.values, \n", "               color=['skyblue', 'lightcoral', 'lightgreen'], alpha=0.8)\n", "ax2.set_title('类别频数分布')\n", "ax2.set_xlabel('类别')\n", "ax2.set_ylabel('频数')\n", "\n", "# 添加数值标签\n", "for bar, count in zip(bars, class_counts.values):\n", "    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 20, \n", "             str(count), ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据分布特征\n", "skew_values = df[continuous_vars].skew()\n", "kurt_values = df[continuous_vars].kurtosis()\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "ax1.hist(skew_values, bins=20, alpha=0.7, color='steelblue', edgecolor='black')\n", "ax1.axvline(skew_values.mean(), color='red', linestyle='--', \n", "           label=f'均值: {skew_values.mean():.2f}')\n", "ax1.set_title('变量偏度分布')\n", "ax1.set_xlabel('偏度')\n", "ax1.set_ylabel('频数')\n", "ax1.legend()\n", "ax1.grid(True, alpha=0.3)\n", "\n", "ax2.hist(kurt_values, bins=20, alpha=0.7, color='coral', edgecolor='black')\n", "ax2.axvline(kurt_values.mean(), color='red', linestyle='--', \n", "           label=f'均值: {kurt_values.mean():.2f}')\n", "ax2.set_title('变量峰度分布')\n", "ax2.set_xlabel('峰度')\n", "ax2.set_ylabel('频数')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"偏度范围: [{skew_values.min():.2f}, {skew_values.max():.2f}]\")\n", "print(f\"峰度范围: [{kurt_values.min():.2f}, {kurt_values.max():.2f}]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 变量关系分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 相关性分析\n", "corr_matrix = df[continuous_vars].corr()\n", "\n", "plt.figure(figsize=(16, 14))\n", "mask = np.triu(np.ones_like(corr_matrix, dtype=bool))\n", "sns.heatmap(corr_matrix, mask=mask, cmap='RdBu_r', center=0,\n", "            square=True, linewidths=0.1, cbar_kws={\"shrink\": .8})\n", "plt.title('变量相关性矩阵', fontsize=16, pad=20)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 找出强相关变量对\n", "high_corr_pairs = []\n", "for i in range(len(corr_matrix.columns)):\n", "    for j in range(i+1, len(corr_matrix.columns)):\n", "        corr_val = corr_matrix.iloc[i, j]\n", "        if abs(corr_val) > 0.7:\n", "            high_corr_pairs.append((corr_matrix.columns[i], \n", "                                  corr_matrix.columns[j], corr_val))\n", "\n", "print(f\"发现 {len(high_corr_pairs)} 对强相关变量 (|r| > 0.7):\")\n", "for var1, var2, corr in high_corr_pairs[:10]:\n", "    print(f\"  {var1} - {var2}: {corr:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 不同类别下连续变量的分布差异\n", "test_vars = ['1', '5', '15', '25', '35', '45']\n", "\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.flatten()\n", "\n", "for i, var in enumerate(test_vars):\n", "    for class_val in sorted(df['10'].unique()):\n", "        data = df[df['10'] == class_val][var]\n", "        axes[i].hist(data, alpha=0.6, label=f'类别 {class_val}', bins=30)\n", "    \n", "    axes[i].set_title(f'变量 {var} 在不同类别下的分布')\n", "    axes[i].set_xlabel('数值')\n", "    axes[i].set_ylabel('频数')\n", "    axes[i].legend()\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 方差分析 - 检验不同类别间的显著性差异\n", "anova_results = []\n", "unique_classes = sorted(df['10'].unique())\n", "\n", "for var in continuous_vars:\n", "    groups = [df[df['10'] == cls][var].dropna() for cls in unique_classes]\n", "    \n", "    if all(len(group) > 0 for group in groups):\n", "        try:\n", "            f_stat, p_val = f_oneway(*groups)\n", "            anova_results.append({\n", "                '变量': var,\n", "                'F统计量': f_stat,\n", "                'p值': p_val,\n", "                '显著': 'Yes' if p_val < 0.05 else 'No'\n", "            })\n", "        except:\n", "            continue\n", "\n", "anova_df = pd.DataFrame(anova_results).sort_values('p值')\n", "print(\"方差分析结果 (前15个最显著变量):\")\n", "print(anova_df.head(15).round(6))\n", "\n", "significant_vars = anova_df[anova_df['显著'] == 'Yes']['变量'].tolist()\n", "print(f\"\\n显著差异变量数量: {len(significant_vars)} / {len(continuous_vars)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 时间序列特征"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 平稳性检验\n", "stationarity_results = []\n", "test_variables = ['1', '5', '15', '25', '35', '45', '55']\n", "\n", "for var in test_variables:\n", "    if var in df.columns:\n", "        try:\n", "            adf_result = adfuller(df[var].dropna())\n", "            stationarity_results.append({\n", "                '变量': var,\n", "                'ADF统计量': adf_result[0],\n", "                'p值': adf_result[1],\n", "                '1%临界值': adf_result[4]['1%'],\n", "                '5%临界值': adf_result[4]['5%'],\n", "                '平稳': 'Yes' if adf_result[1] < 0.05 else 'No'\n", "            })\n", "        except:\n", "            continue\n", "\n", "stationarity_df = pd.DataFrame(stationarity_results)\n", "print(\"ADF平稳性检验结果:\")\n", "print(stationarity_df.round(4))\n", "\n", "stationary_count = len(stationarity_df[stationarity_df['平稳'] == 'Yes'])\n", "print(f\"\\n平稳序列: {stationary_count} / {len(stationarity_df)}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 自相关分析\n", "acf_vars = ['1', '15', '35']\n", "\n", "fig, axes = plt.subplots(len(acf_vars), 2, figsize=(15, 4*len(acf_vars)))\n", "if len(acf_vars) == 1:\n", "    axes = axes.reshape(1, -1)\n", "\n", "for i, var in enumerate(acf_vars):\n", "    try:\n", "        plot_acf(df[var].dropna(), ax=axes[i, 0], lags=40, alpha=0.05)\n", "        axes[i, 0].set_title(f'变量 {var} - 自相关函数')\n", "        \n", "        plot_pacf(df[var].dropna(), ax=axes[i, 1], lags=40, alpha=0.05)\n", "        axes[i, 1].set_title(f'变量 {var} - 偏自相关函数')\n", "    except:\n", "        axes[i, 0].text(0.5, 0.5, f'变量 {var} 计算失败', \n", "                       transform=axes[i, 0].transAxes, ha='center')\n", "        axes[i, 1].text(0.5, 0.5, f'变量 {var} 计算失败', \n", "                       transform=axes[i, 1].transAxes, ha='center')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 降维分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# PCA主成分分析\n", "scaler = StandardScaler()\n", "scaled_data = scaler.fit_transform(df[continuous_vars].fillna(df[continuous_vars].mean()))\n", "\n", "pca = PCA()\n", "pca_result = pca.fit_transform(scaled_data)\n", "cumvar_ratio = np.cumsum(pca.explained_variance_ratio_)\n", "\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))\n", "\n", "# 解释方差比\n", "ax1.bar(range(1, 21), pca.explained_variance_ratio_[:20], alpha=0.8)\n", "ax1.set_title('前20个主成分解释方差比')\n", "ax1.set_xlabel('主成分')\n", "ax1.set_ylabel('解释方差比')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# 累积解释方差\n", "ax2.plot(range(1, 51), cumvar_ratio[:50], 'o-', markersize=4)\n", "ax2.axhline(y=0.8, color='red', linestyle='--', label='80%')\n", "ax2.axhline(y=0.9, color='orange', linestyle='--', label='90%')\n", "ax2.set_title('累积解释方差比')\n", "ax2.set_xlabel('主成分数量')\n", "ax2.set_ylabel('累积解释方差比')\n", "ax2.legend()\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 计算所需主成分数量\n", "n_comp_80 = np.argmax(cumvar_ratio >= 0.8) + 1\n", "n_comp_90 = np.argmax(cumvar_ratio >= 0.9) + 1\n", "\n", "print(f\"解释80%方差需要: {n_comp_80} 个主成分\")\n", "print(f\"解释90%方差需要: {n_comp_90} 个主成分\")\n", "print(f\"前10个主成分解释方差: {cumvar_ratio[9]:.3f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 主成分载荷分析\n", "n_components = 4\n", "components_df = pd.DataFrame(\n", "    pca.components_[:n_components].T,\n", "    columns=[f'PC{i+1}' for i in range(n_components)],\n", "    index=continuous_vars\n", ")\n", "\n", "fig, axes = plt.subplots(1, n_components, figsize=(20, 5))\n", "\n", "for i in range(n_components):\n", "    loadings = components_df[f'PC{i+1}'].abs().sort_values(ascending=True)\n", "    top_loadings = loadings.tail(10)\n", "    \n", "    axes[i].barh(range(len(top_loadings)), top_loadings.values, alpha=0.8)\n", "    axes[i].set_yticks(range(len(top_loadings)))\n", "    axes[i].set_yticklabels([f'Var{idx}' for idx in top_loadings.index])\n", "    axes[i].set_title(f'PC{i+1} 主要载荷\\n(解释方差: {pca.explained_variance_ratio_[i]:.3f})')\n", "    axes[i].set_xlabel('载荷绝对值')\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 机器学习建模"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分类模型 - 预测第10列\n", "X = df[continuous_vars].fillna(df[continuous_vars].mean())\n", "y = df['10']\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X, y, test_size=0.2, random_state=42, stratify=y\n", ")\n", "\n", "# 标准化\n", "scaler_ml = StandardScaler()\n", "X_train_scaled = scaler_ml.fit_transform(X_train)\n", "X_test_scaled = scaler_ml.transform(X_test)\n", "\n", "# 训练随机森林分类器\n", "rf_clf = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)\n", "rf_clf.fit(X_train_scaled, y_train)\n", "\n", "# 预测和评估\n", "y_pred = rf_clf.predict(X_test_scaled)\n", "accuracy = accuracy_score(y_test, y_pred)\n", "\n", "print(\"分类模型性能:\")\n", "print(f\"准确率: {accuracy:.3f}\")\n", "print(\"\\n详细报告:\")\n", "print(classification_report(y_test, y_pred))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 特征重要性分析\n", "feature_importance = pd.DataFrame({\n", "    '变量': continuous_vars,\n", "    '重要性': rf_clf.feature_importances_\n", "}).sort_values('重要性', ascending=False)\n", "\n", "plt.figure(figsize=(12, 8))\n", "top_features = feature_importance.head(20)\n", "plt.barh(range(len(top_features)), top_features['重要性'], alpha=0.8)\n", "plt.yticks(range(len(top_features)), [f'变量{var}' for var in top_features['变量']])\n", "plt.title('特征重要性排名 (前20个)', fontsize=14)\n", "plt.xlabel('重要性得分')\n", "plt.gca().invert_yaxis()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"最重要的5个变量: {list(top_features['变量'].head(5))}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 时间序列预测模型\n", "target_series = df[continuous_vars[0]].fillna(df[continuous_vars[0]].mean()).values\n", "\n", "def create_sequences(data, seq_length=10):\n", "    X, y = [], []\n", "    for i in range(seq_length, len(data)):\n", "        X.append(data[i-seq_length:i])\n", "        y.append(data[i])\n", "    return np.array(X), np.array(y)\n", "\n", "# 创建序列数据\n", "seq_length = 10\n", "X_seq, y_seq = create_sequences(target_series, seq_length)\n", "\n", "# 时间序列分割\n", "split_idx = int(0.8 * len(X_seq))\n", "X_train_seq, X_test_seq = X_seq[:split_idx], X_seq[split_idx:]\n", "y_train_seq, y_test_seq = y_seq[:split_idx], y_seq[split_idx:]\n", "\n", "# 训练回归模型\n", "rf_reg = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)\n", "rf_reg.fit(X_train_seq, y_train_seq)\n", "\n", "# 预测\n", "y_pred_seq = rf_reg.predict(X_test_seq)\n", "\n", "# 评估\n", "mse = mean_squared_error(y_test_seq, y_pred_seq)\n", "rmse = np.sqrt(mse)\n", "r2 = r2_score(y_test_seq, y_pred_seq)\n", "\n", "print(f\"时间序列预测性能 (变量{continuous_vars[0]}):\")\n", "print(f\"RMSE: {rmse:.4f}\")\n", "print(f\"R²: {r2:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 预测结果可视化\n", "plt.figure(figsize=(15, 8))\n", "test_indices = range(split_idx + seq_length, len(target_series))\n", "plt.plot(test_indices, y_test_seq, label='实际值', alpha=0.8, linewidth=1.5)\n", "plt.plot(test_indices, y_pred_seq, label='预测值', alpha=0.8, linewidth=1.5)\n", "plt.title(f'变量{continuous_vars[0]} 时间序列预测结果', fontsize=14)\n", "plt.xlabel('时间索引')\n", "plt.ylabel('数值')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 结果总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析结果汇总\n", "print(\"=\" * 60)\n", "print(\"高维时间序列数据分析 - 主要发现\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n1. 数据基本特征:\")\n", "print(f\"   • 数据规模: {df.shape[0]} 个观测点, {len(continuous_vars)} 个连续变量\")\n", "print(f\"   • 分类变量分布: {dict(df['10'].value_counts().sort_index())}\")\n", "print(f\"   • 数据完整性: 100% (无缺失值)\")\n", "\n", "print(\"\\n2. 变量关系特征:\")\n", "if 'high_corr_pairs' in locals():\n", "    print(f\"   • 强相关变量对: {len(high_corr_pairs)} 对 (|r| > 0.7)\")\n", "if 'significant_vars' in locals():\n", "    print(f\"   • 类别间显著差异变量: {len(significant_vars)} / {len(continuous_vars)}\")\n", "\n", "print(\"\\n3. 时间序列特征:\")\n", "if 'stationary_count' in locals():\n", "    print(f\"   • 平稳序列比例: {stationary_count} / {len(stationarity_df)}\")\n", "\n", "print(\"\\n4. 降维分析结果:\")\n", "if 'n_comp_80' in locals():\n", "    print(f\"   • 解释80%方差需要: {n_comp_80} 个主成分\")\n", "    print(f\"   • 解释90%方差需要: {n_comp_90} 个主成分\")\n", "    print(f\"   • 降维效果: 显著 (压缩比 {n_comp_80/len(continuous_vars):.2f})\")\n", "\n", "print(\"\\n5. 预测模型性能:\")\n", "if 'accuracy' in locals():\n", "    print(f\"   • 分类准确率: {accuracy:.3f}\")\n", "if 'r2' in locals():\n", "    print(f\"   • 时间序列预测R²: {r2:.3f}\")\n", "    print(f\"   • 时间序列预测RMSE: {rmse:.4f}\")\n", "\n", "print(\"\\n6. 关键洞察:\")\n", "print(\"   • 数据表现出明显的时间依赖性\")\n", "print(\"   • 变量间存在复杂的相关结构\")\n", "print(\"   • 分类变量与连续变量有显著关联\")\n", "print(\"   • 模型具有良好的预测能力\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 业务建议\n", "\n", "### 数据质量\n", "- 数据完整性良好，无需额外的清洗工作\n", "- 建议定期监控数据质量，确保持续的高质量输入\n", "\n", "### 特征工程\n", "- 可以利用PCA降维技术减少计算复杂度\n", "- 重点关注特征重要性排名靠前的变量\n", "- 考虑构建时间滞后特征以提升预测性能\n", "\n", "### 模型应用\n", "- 分类模型可用于状态识别和风险预警\n", "- 时间序列模型适合短期趋势预测\n", "- 建议结合多个模型进行集成预测\n", "\n", "### 监控策略\n", "- 建立模型性能监控机制\n", "- 定期重新训练模型以适应数据变化\n", "- 关注关键变量的异常波动\n", "\n", "### 进一步分析方向\n", "- 深入研究变量间的因果关系\n", "- 探索非线性模型的应用潜力\n", "- 考虑引入外部数据源丰富分析维度"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}