#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股与转债动量效应验证分析
验证A股动量效应不如转债市场的动量效应强
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载验证数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel('/Users/<USER>/Desktop/验证.xlsx')
        
        # 设置列名
        df.columns = ['日期', '国证2000', '万得等权重指数', '转债对应正股指数']
        
        # 转换日期格式
        df['日期'] = pd.to_datetime(df['日期'])
        df.set_index('日期', inplace=True)
        
        # 删除缺失值
        df = df.dropna()
        
        print(f"数据加载成功，时间范围：{df.index[0]} 到 {df.index[-1]}")
        print(f"数据形状：{df.shape}")
        print("\n数据预览：")
        print(df.head())
        
        return df
    except Exception as e:
        print(f"数据加载失败：{e}")
        return None

def calculate_momentum_signal(price_series, lookback_period=20):
    """
    计算动量信号
    lookback_period: 回望期，默认20个交易日
    """
    # 计算收益率
    returns = price_series.pct_change()
    
    # 计算动量信号（过去N天的累计收益率）
    momentum = returns.rolling(window=lookback_period).sum()
    
    # 生成交易信号：动量为正时买入(1)，为负时卖出(0)
    signal = (momentum > 0).astype(int)
    
    return signal, momentum

def calculate_strategy_returns(price_series, signal):
    """计算策略收益率"""
    # 计算日收益率
    daily_returns = price_series.pct_change()
    
    # 策略收益 = 信号 * 日收益率
    # 信号为1时持有，信号为0时空仓（假设空仓收益为0）
    strategy_returns = signal.shift(1) * daily_returns
    
    # 计算累计收益率（净值）
    cumulative_returns = (1 + strategy_returns.fillna(0)).cumprod()
    
    return strategy_returns, cumulative_returns

def calculate_performance_metrics(returns_series, cumulative_returns):
    """计算绩效指标"""
    # 去除缺失值
    returns_clean = returns_series.dropna()
    
    if len(returns_clean) == 0:
        return {}
    
    # 年化收益率
    total_return = cumulative_returns.iloc[-1] - 1
    years = len(returns_clean) / 252  # 假设252个交易日
    annual_return = (1 + total_return) ** (1/years) - 1
    
    # 年化波动率
    annual_volatility = returns_clean.std() * np.sqrt(252)
    
    # 最大回撤
    running_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - running_max) / running_max
    max_drawdown = drawdown.min()
    
    # 夏普比率（假设无风险利率为3%）
    risk_free_rate = 0.03
    sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
    
    # 卡玛比率
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
    
    # 胜率
    win_rate = (returns_clean > 0).mean()
    
    return {
        '年化收益率': annual_return,
        '年化波动率': annual_volatility,
        '最大回撤': max_drawdown,
        '夏普比率': sharpe_ratio,
        '卡玛比率': calmar_ratio,
        '胜率': win_rate
    }

def main():
    """主函数"""
    # 创建输出文件夹
    output_dir = '/Users/<USER>/Desktop/动量验证'
    os.makedirs(output_dir, exist_ok=True)
    
    # 加载数据
    df = load_data()
    if df is None:
        return
    
    # 设置动量参数
    lookback_period = 20  # 20日动量
    
    # 存储结果
    results = {}
    signals = {}
    cumulative_returns_dict = {}
    
    # 对每个指数计算动量策略
    for column in df.columns:
        print(f"\n处理指数：{column}")
        
        # 计算动量信号
        signal, momentum = calculate_momentum_signal(df[column], lookback_period)
        signals[column] = signal
        
        # 计算策略收益
        strategy_returns, cumulative_returns = calculate_strategy_returns(df[column], signal)
        cumulative_returns_dict[column] = cumulative_returns
        
        # 计算绩效指标
        metrics = calculate_performance_metrics(strategy_returns, cumulative_returns)
        results[column] = metrics
        
        print(f"年化收益率: {metrics['年化收益率']:.2%}")
        print(f"最大回撤: {metrics['最大回撤']:.2%}")
        print(f"夏普比率: {metrics['夏普比率']:.2f}")
    
    # 创建结果DataFrame
    results_df = pd.DataFrame(results).T
    
    # 格式化百分比
    for col in ['年化收益率', '年化波动率', '最大回撤', '胜率']:
        results_df[col] = results_df[col].apply(lambda x: f"{x:.2%}")
    
    # 格式化比率
    for col in ['夏普比率', '卡玛比率']:
        results_df[col] = results_df[col].apply(lambda x: f"{x:.2f}")
    
    # 保存结果到Excel
    excel_path = os.path.join(output_dir, '动量策略择时结果.xlsx')
    results_df.to_excel(excel_path)
    print(f"\n结果已保存到：{excel_path}")
    
    return df, signals, cumulative_returns_dict, results_df, output_dir

def plot_cumulative_returns(cumulative_returns_dict, output_dir):
    """绘制累计收益率图"""
    plt.figure(figsize=(14, 8))

    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']

    for i, (name, cum_ret) in enumerate(cumulative_returns_dict.items()):
        plt.plot(cum_ret.index, cum_ret, label=f'{name}择时策略',
                linewidth=2, color=colors[i])

    plt.title('三个指数动量择时策略净值对比', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('累计净值', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()

    # 保存图片
    plt.savefig(os.path.join(output_dir, '择时净值对比图.png'),
                dpi=300, bbox_inches='tight')
    plt.show()

def plot_drawdown_comparison(cumulative_returns_dict, output_dir):
    """绘制回撤对比图"""
    plt.figure(figsize=(14, 8))

    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']

    for i, (name, cum_ret) in enumerate(cumulative_returns_dict.items()):
        # 计算回撤
        running_max = cum_ret.expanding().max()
        drawdown = (cum_ret - running_max) / running_max

        plt.plot(drawdown.index, drawdown * 100, label=f'{name}',
                linewidth=2, color=colors[i])

    plt.title('三个指数动量择时策略回撤对比', fontsize=16, fontweight='bold')
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('回撤 (%)', fontsize=12)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.tight_layout()

    # 保存图片
    plt.savefig(os.path.join(output_dir, '回撤对比图.png'),
                dpi=300, bbox_inches='tight')
    plt.show()

def analyze_momentum_effectiveness(results_df):
    """分析动量效应强弱"""
    print("\n=== 动量效应分析 ===")
    print("根据择时策略绩效指标分析：")

    # 提取数值进行比较
    metrics = {}
    for index in results_df.index:
        metrics[index] = {
            '年化收益率': float(results_df.loc[index, '年化收益率'].strip('%')) / 100,
            '夏普比率': float(results_df.loc[index, '夏普比率']),
            '最大回撤': abs(float(results_df.loc[index, '最大回撤'].strip('%')) / 100)
        }

    # 按夏普比率排序
    sorted_by_sharpe = sorted(metrics.items(), key=lambda x: x[1]['夏普比率'], reverse=True)

    print("\n按夏普比率排序（动量效应强弱）：")
    for i, (name, metric) in enumerate(sorted_by_sharpe, 1):
        print(f"{i}. {name}: 夏普比率 {metric['夏普比率']:.2f}, "
              f"年化收益率 {metric['年化收益率']:.2%}, "
              f"最大回撤 {metric['最大回撤']:.2%}")

    # 验证结论
    convertible_bond_sharpe = metrics['万得等权重指数']['夏普比率']
    stock_indices = ['国证2000', '转债对应正股指数']

    print(f"\n=== 验证结论 ===")
    print(f"万得等权重指数（转债）夏普比率: {convertible_bond_sharpe:.2f}")

    for stock_index in stock_indices:
        stock_sharpe = metrics[stock_index]['夏普比率']
        print(f"{stock_index}（A股）夏普比率: {stock_sharpe:.2f}")

        if convertible_bond_sharpe > stock_sharpe:
            print(f"✓ 验证成功：{stock_index}的动量效应({stock_sharpe:.2f})确实不如万得等权重指数({convertible_bond_sharpe:.2f})")
        else:
            print(f"✗ 验证失败：{stock_index}的动量效应({stock_sharpe:.2f})强于万得等权重指数({convertible_bond_sharpe:.2f})")

if __name__ == "__main__":
    # 运行主分析
    df, signals, cumulative_returns_dict, results_df, output_dir = main()

    # 绘制图表
    plot_cumulative_returns(cumulative_returns_dict, output_dir)
    plot_drawdown_comparison(cumulative_returns_dict, output_dir)

    # 分析动量效应
    analyze_momentum_effectiveness(results_df)

    print(f"\n所有结果已保存到：{output_dir}")
    print("\n分析完成！")
