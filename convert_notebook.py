#!/usr/bin/env python3
"""
将Jupyter Notebook转换为HTML格式的脚本
用于笔试提交
"""

import subprocess
import sys
import os

def convert_notebook_to_html():
    """将notebook转换为HTML格式"""
    
    # 检查是否安装了nbconvert
    try:
        import nbconvert
    except ImportError:
        print("正在安装nbconvert...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "nbconvert"])
    
    # 输入和输出文件名
    input_file = "高维时间序列数据分析.ipynb"
    output_file = "Cui_Wei.html"  # 按照要求的格式命名
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 找不到文件 {input_file}")
        return False
    
    try:
        # 执行转换命令
        cmd = [
            "jupyter", "nbconvert", 
            "--to", "html",
            "--output", output_file,
            input_file
        ]
        
        print(f"正在将 {input_file} 转换为 {output_file}...")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"转换成功! 输出文件: {output_file}")
            print(f"文件大小: {os.path.getsize(output_file) / 1024:.2f} KB")
            return True
        else:
            print(f"转换失败: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("错误: 找不到jupyter命令，请确保已安装Jupyter")
        print("可以运行: pip install jupyter")
        return False
    except Exception as e:
        print(f"转换过程中出现错误: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Jupyter Notebook to HTML 转换工具")
    print("=" * 50)
    
    success = convert_notebook_to_html()
    
    if success:
        print("\n转换完成! 请检查生成的HTML文件。")
        print("注意事项:")
        print("1. 请确保所有代码单元格都已执行")
        print("2. 检查图表和输出是否正确显示")
        print("3. 确认文件命名符合要求 (Firstname_Lastname.html)")
    else:
        print("\n转换失败，请检查错误信息并重试。")

if __name__ == "__main__":
    main()
