# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, mean_squared_error, r2_score
from statsmodels.tsa.stattools import adfuller, grangercausalitytests
from statsmodels.stats.diagnostic import acorr_ljungbox
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

# 设置中文字体和图表样式
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
except:
    # 如果中文字体不可用，使用默认字体
    print('中文字体设置失败，使用默认字体')
    pass
sns.set_style("whitegrid")
warnings.filterwarnings('ignore')

print("库导入完成")

# 数据加载
data_path = '/Users/<USER>/Desktop/TEST_Trader_Quant_dataset.csv'
df = pd.read_csv(data_path)

print(f"数据集形状: {df.shape}")
print(f"列名: {list(df.columns)}")
print("\n数据集基本信息:")
print(df.info())

# 数据质量检查
print("缺失值统计:")
missing_stats = df.isnull().sum()
missing_percent = (missing_stats / len(df)) * 100
missing_df = pd.DataFrame({
    '缺失数量': missing_stats,
    '缺失百分比': missing_percent
})
print(missing_df[missing_df['缺失数量'] > 0])

# 检查第10列的离散值分布
print("\n第10列（离散变量）的值分布:")
print(df['10'].value_counts().sort_index())
print(f"第10列唯一值: {sorted(df['10'].unique())}")

# 基础统计描述
print("数据集描述性统计:")
desc_stats = df.describe()
print(desc_stats.round(4))

# 识别连续变量和离散变量
continuous_cols = [col for col in df.columns if col != '10']
discrete_col = '10'

print(f"\n连续变量数量: {len(continuous_cols)}")
print(f"离散变量: {discrete_col}")

# 2.1 时间序列趋势分析
# 创建时间索引（假设数据按时间顺序排列）
df['time_index'] = range(len(df))

# 选择几个代表性变量进行时间序列可视化
sample_cols = ['1', '5', '10', '15', '25', '35', '45', '55']

fig, axes = plt.subplots(2, 4, figsize=(20, 10))
axes = axes.ravel()

for i, col in enumerate(sample_cols):
    axes[i].plot(df['time_index'], df[col], alpha=0.7, linewidth=0.8)
    axes[i].set_title(f'变量{col}的时间序列趋势', fontsize=12)
    axes[i].set_xlabel('时间索引')
    axes[i].set_ylabel('数值')
    axes[i].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("时间序列趋势分析完成")

# 2.2 离散变量（第10列）的时间分布分析
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# 离散变量的时间序列图
ax1.scatter(df['time_index'], df['10'], alpha=0.6, s=1)
ax1.set_title('第10列离散变量的时间分布', fontsize=14)
ax1.set_xlabel('时间索引')
ax1.set_ylabel('离散值 (0, 1, 2)')
ax1.grid(True, alpha=0.3)

# 离散变量的分布直方图
value_counts = df['10'].value_counts().sort_index()
ax2.bar(value_counts.index, value_counts.values, alpha=0.7, color=['skyblue', 'lightcoral', 'lightgreen'])
ax2.set_title('第10列离散变量的频数分布', fontsize=14)
ax2.set_xlabel('离散值')
ax2.set_ylabel('频数')
ax2.grid(True, alpha=0.3)

# 添加数值标签
for i, v in enumerate(value_counts.values):
    ax2.text(value_counts.index[i], v + 50, str(v), ha='center', va='bottom')

plt.tight_layout()
plt.show()

print(f"离散变量分布: {dict(value_counts)}")

# 2.3 数据分布特征分析
# 计算偏度和峰度
skewness = df[continuous_cols].skew()
kurtosis = df[continuous_cols].kurtosis()

distribution_stats = pd.DataFrame({
    '偏度': skewness,
    '峰度': kurtosis
})

# 可视化偏度和峰度分布
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

ax1.hist(skewness, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
ax1.axvline(skewness.mean(), color='red', linestyle='--', label=f'均值: {skewness.mean():.2f}')
ax1.set_title('各变量偏度分布', fontsize=14)
ax1.set_xlabel('偏度')
ax1.set_ylabel('频数')
ax1.legend()
ax1.grid(True, alpha=0.3)

ax2.hist(kurtosis, bins=20, alpha=0.7, color='lightcoral', edgecolor='black')
ax2.axvline(kurtosis.mean(), color='red', linestyle='--', label=f'均值: {kurtosis.mean():.2f}')
ax2.set_title('各变量峰度分布', fontsize=14)
ax2.set_xlabel('峰度')
ax2.set_ylabel('频数')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("分布特征分析完成")

# 3.1 相关性矩阵分析
# 计算连续变量间的相关性
correlation_matrix = df[continuous_cols].corr()

# 创建相关性热力图
plt.figure(figsize=(16, 14))
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # 只显示下三角
sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='RdBu_r', center=0,
            square=True, linewidths=0.1, cbar_kws={"shrink": .8})
plt.title('变量间相关性热力图', fontsize=16, pad=20)
plt.tight_layout()
plt.show()

# 找出高相关性的变量对
high_corr_pairs = []
for i in range(len(correlation_matrix.columns)):
    for j in range(i+1, len(correlation_matrix.columns)):
        corr_val = correlation_matrix.iloc[i, j]
        if abs(corr_val) > 0.7:  # 高相关性阈值
            high_corr_pairs.append((correlation_matrix.columns[i], 
                                  correlation_matrix.columns[j], 
                                  corr_val))

print(f"发现 {len(high_corr_pairs)} 对高相关性变量（|r| > 0.7）:")
for pair in high_corr_pairs[:10]:  # 显示前10对
    print(f"变量{pair[0]} - 变量{pair[1]}: r = {pair[2]:.3f}")

# 3.2 离散变量与连续变量的关系分析
# 按离散变量分组分析连续变量的分布
discrete_groups = df.groupby('10')

# 选择几个代表性连续变量进行分析
sample_continuous = ['1', '5', '15', '25', '35', '45']

fig, axes = plt.subplots(2, 3, figsize=(18, 12))
axes = axes.ravel()

for i, col in enumerate(sample_continuous):
    for group_val in [0, 1, 2]:
        group_data = df[df['10'] == group_val][col]
        axes[i].hist(group_data, alpha=0.6, label=f'类别{group_val}', bins=30)
    
    axes[i].set_title(f'变量{col}在不同离散类别下的分布', fontsize=12)
    axes[i].set_xlabel('数值')
    axes[i].set_ylabel('频数')
    axes[i].legend()
    axes[i].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("离散变量与连续变量关系分析完成")

# 3.3 统计显著性检验
# 对每个连续变量进行方差分析（ANOVA），检验不同离散类别间是否存在显著差异
from scipy.stats import f_oneway

anova_results = []
for col in continuous_cols:
    group_0 = df[df['10'] == 0][col].dropna()
    group_1 = df[df['10'] == 1][col].dropna()
    group_2 = df[df['10'] == 2][col].dropna()
    
    if len(group_0) > 0 and len(group_1) > 0 and len(group_2) > 0:
        f_stat, p_value = f_oneway(group_0, group_1, group_2)
        anova_results.append({
            '变量': col,
            'F统计量': f_stat,
            'p值': p_value,
            '显著性': 'Yes' if p_value < 0.05 else 'No'
        })

anova_df = pd.DataFrame(anova_results)
anova_df = anova_df.sort_values('p值')

print("方差分析结果（前20个最显著的变量）:")
print(anova_df.head(20).round(6))

significant_vars = anova_df[anova_df['显著性'] == 'Yes']['变量'].tolist()
print(f"\n共有 {len(significant_vars)} 个变量在不同离散类别间存在显著差异")

# 4.1 平稳性检验（ADF检验）
# 对主要变量进行平稳性检验
adf_results = []
test_cols = ['1', '5', '10', '15', '25', '35', '45', '55']  # 选择代表性变量

for col in test_cols:
    if col != '10':  # 跳过离散变量
        result = adfuller(df[col].dropna())
        adf_results.append({
            '变量': col,
            'ADF统计量': result[0],
            'p值': result[1],
            '临界值_1%': result[4]['1%'],
            '临界值_5%': result[4]['5%'],
            '平稳性': 'Yes' if result[1] < 0.05 else 'No'
        })

adf_df = pd.DataFrame(adf_results)
print("ADF平稳性检验结果:")
print(adf_df.round(6))

stationary_vars = adf_df[adf_df['平稳性'] == 'Yes']['变量'].tolist()
non_stationary_vars = adf_df[adf_df['平稳性'] == 'No']['变量'].tolist()
print(f"\n平稳序列: {stationary_vars}")
print(f"非平稳序列: {non_stationary_vars}")

# 4.2 自相关性分析
from statsmodels.tsa.stattools import acf, pacf
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf

# 选择几个变量进行自相关分析
acf_vars = ['1', '15', '35', '55']

fig, axes = plt.subplots(len(acf_vars), 2, figsize=(15, 4*len(acf_vars)))

for i, col in enumerate(acf_vars):
    # 自相关函数
    plot_acf(df[col].dropna(), ax=axes[i, 0], lags=40, alpha=0.05)
    axes[i, 0].set_title(f'变量{col}的自相关函数(ACF)', fontsize=12)
    
    # 偏自相关函数
    plot_pacf(df[col].dropna(), ax=axes[i, 1], lags=40, alpha=0.05)
    axes[i, 1].set_title(f'变量{col}的偏自相关函数(PACF)', fontsize=12)

plt.tight_layout()
plt.show()

print("自相关性分析完成")

# 4.3 变化点检测
# 使用滑动窗口方法检测潜在的结构性变化
def detect_change_points(series, window_size=100, threshold=2):
    """检测时间序列中的变化点"""
    change_points = []
    
    for i in range(window_size, len(series) - window_size):
        before_window = series[i-window_size:i]
        after_window = series[i:i+window_size]
        
        # 计算均值差异的标准化值
        mean_diff = abs(before_window.mean() - after_window.mean())
        pooled_std = np.sqrt((before_window.var() + after_window.var()) / 2)
        
        if pooled_std > 0:
            normalized_diff = mean_diff / pooled_std
            if normalized_diff > threshold:
                change_points.append(i)
    
    return change_points

# 对几个主要变量检测变化点
change_point_vars = ['1', '15', '35']
change_points_dict = {}

for col in change_point_vars:
    change_points = detect_change_points(df[col])
    change_points_dict[col] = change_points
    print(f"变量{col}检测到 {len(change_points)} 个潜在变化点")

# 可视化变化点
fig, axes = plt.subplots(len(change_point_vars), 1, figsize=(15, 4*len(change_point_vars)))
if len(change_point_vars) == 1:
    axes = [axes]

for i, col in enumerate(change_point_vars):
    axes[i].plot(df['time_index'], df[col], alpha=0.7, linewidth=0.8)
    
    # 标记变化点
    for cp in change_points_dict[col]:
        axes[i].axvline(x=cp, color='red', linestyle='--', alpha=0.7)
    
    axes[i].set_title(f'变量{col}的时间序列及变化点检测', fontsize=12)
    axes[i].set_xlabel('时间索引')
    axes[i].set_ylabel('数值')
    axes[i].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("变化点检测分析完成")

# 5.1 主成分分析（PCA）
# 数据标准化
scaler = StandardScaler()
scaled_data = scaler.fit_transform(df[continuous_cols])

# 执行PCA
pca = PCA()
pca_result = pca.fit_transform(scaled_data)

# 计算累积解释方差比
cumulative_variance_ratio = np.cumsum(pca.explained_variance_ratio_)

# 可视化解释方差
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

# 解释方差比
ax1.bar(range(1, min(21, len(pca.explained_variance_ratio_)+1)), 
        pca.explained_variance_ratio_[:20], alpha=0.7)
ax1.set_title('前20个主成分的解释方差比', fontsize=14)
ax1.set_xlabel('主成分')
ax1.set_ylabel('解释方差比')
ax1.grid(True, alpha=0.3)

# 累积解释方差比
ax2.plot(range(1, min(51, len(cumulative_variance_ratio)+1)), 
         cumulative_variance_ratio[:50], 'bo-', markersize=4)
ax2.axhline(y=0.8, color='red', linestyle='--', label='80%解释方差')
ax2.axhline(y=0.9, color='orange', linestyle='--', label='90%解释方差')
ax2.set_title('累积解释方差比', fontsize=14)
ax2.set_xlabel('主成分数量')
ax2.set_ylabel('累积解释方差比')
ax2.legend()
ax2.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 找到解释80%和90%方差所需的主成分数量
n_components_80 = np.argmax(cumulative_variance_ratio >= 0.8) + 1
n_components_90 = np.argmax(cumulative_variance_ratio >= 0.9) + 1

print(f"解释80%方差需要 {n_components_80} 个主成分")
print(f"解释90%方差需要 {n_components_90} 个主成分")
print(f"前10个主成分解释了 {cumulative_variance_ratio[9]:.3f} 的方差")

# 5.2 主成分载荷分析
# 分析前几个主成分的构成
n_top_components = 5
components_df = pd.DataFrame(
    pca.components_[:n_top_components].T,
    columns=[f'PC{i+1}' for i in range(n_top_components)],
    index=continuous_cols
)

# 可视化主成分载荷
fig, axes = plt.subplots(1, n_top_components, figsize=(20, 4))

for i in range(n_top_components):
    pc_loadings = components_df[f'PC{i+1}'].abs().sort_values(ascending=True)
    top_loadings = pc_loadings.tail(10)  # 显示载荷最大的10个变量
    
    axes[i].barh(range(len(top_loadings)), top_loadings.values, alpha=0.7)
    axes[i].set_yticks(range(len(top_loadings)))
    axes[i].set_yticklabels([f'变量{idx}' for idx in top_loadings.index])
    axes[i].set_title(f'PC{i+1}主要载荷\n(解释方差: {pca.explained_variance_ratio_[i]:.3f})', fontsize=12)
    axes[i].set_xlabel('载荷绝对值')
    axes[i].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

print("主成分载荷分析完成")

# 5.3 聚类分析
# 使用前10个主成分进行K-means聚类
n_components_for_clustering = 10
pca_data_reduced = pca_result[:, :n_components_for_clustering]

# 确定最优聚类数量（肘部法则）
inertias = []
k_range = range(2, 11)

for k in k_range:
    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
    kmeans.fit(pca_data_reduced)
    inertias.append(kmeans.inertia_)

# 可视化肘部法则
plt.figure(figsize=(10, 6))
plt.plot(k_range, inertias, 'bo-', markersize=8)
plt.title('K-means聚类肘部法则', fontsize=14)
plt.xlabel('聚类数量 (k)')
plt.ylabel('簇内平方和 (Inertia)')
plt.grid(True, alpha=0.3)
plt.show()

# 选择最优k值进行聚类
optimal_k = 4  # 根据肘部法则选择
kmeans_final = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
cluster_labels = kmeans_final.fit_predict(pca_data_reduced)

# 将聚类结果添加到数据框
df['cluster'] = cluster_labels

print(f"使用 {optimal_k} 个聚类，聚类分布:")
print(pd.Series(cluster_labels).value_counts().sort_index())

# 6.1 离散变量预测模型（分类问题）
# 使用其他变量预测第10列的离散值

# 准备特征和目标变量
X = df[continuous_cols].fillna(df[continuous_cols].mean())  # 处理缺失值
y = df['10']

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42, stratify=y)

# 特征标准化
scaler_ml = StandardScaler()
X_train_scaled = scaler_ml.fit_transform(X_train)
X_test_scaled = scaler_ml.transform(X_test)

# 训练随机森林分类器
rf_classifier = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
rf_classifier.fit(X_train_scaled, y_train)

# 预测和评估
y_pred = rf_classifier.predict(X_test_scaled)
y_pred_proba = rf_classifier.predict_proba(X_test_scaled)

print("离散变量预测模型性能:")
print(classification_report(y_test, y_pred))

# 特征重要性分析
feature_importance = pd.DataFrame({
    '变量': continuous_cols,
    '重要性': rf_classifier.feature_importances_
}).sort_values('重要性', ascending=False)

# 可视化特征重要性
plt.figure(figsize=(12, 8))
top_features = feature_importance.head(20)
plt.barh(range(len(top_features)), top_features['重要性'], alpha=0.7)
plt.yticks(range(len(top_features)), [f'变量{var}' for var in top_features['变量']])
plt.title('离散变量预测的特征重要性（前20个）', fontsize=14)
plt.xlabel('重要性得分')
plt.gca().invert_yaxis()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print(f"\n最重要的5个预测变量: {list(top_features['变量'].head(5))}")

# 6.2 时间序列预测模型
# 选择一个主要的连续变量进行时间序列预测
target_var = '1'  # 选择变量1作为预测目标
target_series = df[target_var].values

# 创建滞后特征
def create_lag_features(series, n_lags=10):
    """创建滞后特征"""
    features = []
    for i in range(n_lags, len(series)):
        features.append(series[i-n_lags:i])
    return np.array(features), series[n_lags:]

# 创建特征和目标
n_lags = 10
X_ts, y_ts = create_lag_features(target_series, n_lags)

# 划分训练集和测试集（时间序列要保持顺序）
split_idx = int(0.8 * len(X_ts))
X_train_ts, X_test_ts = X_ts[:split_idx], X_ts[split_idx:]
y_train_ts, y_test_ts = y_ts[:split_idx], y_ts[split_idx:]

# 训练随机森林回归器
rf_regressor = RandomForestRegressor(n_estimators=100, random_state=42, max_depth=10)
rf_regressor.fit(X_train_ts, y_train_ts)

# 预测
y_pred_ts = rf_regressor.predict(X_test_ts)

# 评估模型性能
mse = mean_squared_error(y_test_ts, y_pred_ts)
rmse = np.sqrt(mse)
r2 = r2_score(y_test_ts, y_pred_ts)

print(f"时间序列预测模型性能（变量{target_var}）:")
print(f"RMSE: {rmse:.4f}")
print(f"R²: {r2:.4f}")

# 可视化预测结果
plt.figure(figsize=(15, 8))
test_indices = range(split_idx + n_lags, len(target_series))
plt.plot(test_indices, y_test_ts, label='实际值', alpha=0.7, linewidth=1.5)
plt.plot(test_indices, y_pred_ts, label='预测值', alpha=0.7, linewidth=1.5)
plt.title(f'变量{target_var}的时间序列预测结果', fontsize=14)
plt.xlabel('时间索引')
plt.ylabel('数值')
plt.legend()
plt.grid(True, alpha=0.3)
plt.tight_layout()
plt.show()

print("时间序列预测分析完成")

# 7.1 格兰杰因果检验
# 选择几对变量进行格兰杰因果检验
def granger_causality_test(data, var1, var2, max_lags=5):
    """执行格兰杰因果检验"""
    try:
        # 准备数据
        test_data = data[[var1, var2]].dropna()
        
        # 执行格兰杰因果检验
        result = grangercausalitytests(test_data, maxlag=max_lags, verbose=False)
        
        # 提取p值（使用F检验）
        p_values = [result[lag][0]['ssr_ftest'][1] for lag in range(1, max_lags+1)]
        min_p_value = min(p_values)
        best_lag = p_values.index(min_p_value) + 1
        
        return min_p_value, best_lag
    except:
        return None, None

# 选择一些重要变量进行因果关系检验
important_vars = ['1', '5', '15', '25', '35']  # 基于之前的特征重要性分析
causality_results = []

for i, var1 in enumerate(important_vars):
    for j, var2 in enumerate(important_vars):
        if i != j:  # 不检验自己对自己的因果关系
            p_value, lag = granger_causality_test(df, var1, var2)
            if p_value is not None:
                causality_results.append({
                    '原因变量': var1,
                    '结果变量': var2,
                    'p值': p_value,
                    '最佳滞后期': lag,
                    '显著性': 'Yes' if p_value < 0.05 else 'No'
                })

causality_df = pd.DataFrame(causality_results)
causality_df = causality_df.sort_values('p值')

print("格兰杰因果检验结果（显著的因果关系）:")
significant_causality = causality_df[causality_df['显著性'] == 'Yes']
print(significant_causality.round(6))

if len(significant_causality) > 0:
    print(f"\n发现 {len(significant_causality)} 对显著的格兰杰因果关系")
else:
    print("\n在选定变量中未发现显著的格兰杰因果关系")

# 7.2 交叉相关分析
# 分析变量间的滞后相关性
def cross_correlation_analysis(series1, series2, max_lags=20):
    """计算两个时间序列的交叉相关"""
    correlations = []
    lags = range(-max_lags, max_lags + 1)
    
    for lag in lags:
        if lag < 0:
            # 负滞后：series1滞后于series2
            corr = np.corrcoef(series1[-lag:], series2[:lag])[0, 1]
        elif lag > 0:
            # 正滞后：series1领先于series2
            corr = np.corrcoef(series1[:-lag], series2[lag:])[0, 1]
        else:
            # 零滞后：同期相关
            corr = np.corrcoef(series1, series2)[0, 1]
        
        correlations.append(corr)
    
    return lags, correlations

# 选择几对变量进行交叉相关分析
cross_corr_pairs = [('1', '5'), ('1', '15'), ('5', '25')]

fig, axes = plt.subplots(len(cross_corr_pairs), 1, figsize=(12, 4*len(cross_corr_pairs)))
if len(cross_corr_pairs) == 1:
    axes = [axes]

for i, (var1, var2) in enumerate(cross_corr_pairs):
    lags, correlations = cross_correlation_analysis(df[var1].values, df[var2].values)
    
    axes[i].plot(lags, correlations, 'b-', linewidth=2)
    axes[i].axhline(y=0, color='k', linestyle='--', alpha=0.5)
    axes[i].axvline(x=0, color='r', linestyle='--', alpha=0.5)
    axes[i].set_title(f'变量{var1}与变量{var2}的交叉相关分析', fontsize=12)
    axes[i].set_xlabel('滞后期（正值表示变量{}领先）'.format(var1))
    axes[i].set_ylabel('相关系数')
    axes[i].grid(True, alpha=0.3)
    
    # 找到最大相关性及其对应的滞后期
    max_corr_idx = np.argmax(np.abs(correlations))
    max_corr = correlations[max_corr_idx]
    max_lag = lags[max_corr_idx]
    
    axes[i].plot(max_lag, max_corr, 'ro', markersize=8)
    axes[i].text(max_lag, max_corr, f'  最大相关: {max_corr:.3f}\n  滞后期: {max_lag}', 
                fontsize=10, verticalalignment='center')

plt.tight_layout()
plt.show()

print("交叉相关分析完成")

# 8.1 关键发现汇总
print("=" * 60)
print("高维时间序列数据分析 - 关键发现汇总")
print("=" * 60)

print("\n1. 数据基本特征:")
print(f"   - 数据维度: {df.shape[0]} 个时间点 × {len(continuous_cols)} 个连续变量 + 1个离散变量")
print(f"   - 离散变量分布: {dict(df['10'].value_counts().sort_index())}")
print(f"   - 数据完整性: {(1 - df.isnull().sum().sum() / (df.shape[0] * df.shape[1])) * 100:.2f}%")

print("\n2. 时间序列特征:")
if 'stationary_vars' in locals():
    print(f"   - 平稳序列数量: {len(stationary_vars)} / {len(test_cols)-1}")
    print(f"   - 非平稳序列数量: {len(non_stationary_vars)} / {len(test_cols)-1}")

print("\n3. 变量关系特征:")
if 'high_corr_pairs' in locals():
    print(f"   - 高相关性变量对数量: {len(high_corr_pairs)} (|r| > 0.7)")
if 'significant_vars' in locals():
    print(f"   - 与离散变量显著相关的连续变量: {len(significant_vars)} / {len(continuous_cols)}")

print("\n4. 降维分析结果:")
if 'n_components_80' in locals():
    print(f"   - 解释80%方差所需主成分: {n_components_80} / {len(continuous_cols)}")
    print(f"   - 解释90%方差所需主成分: {n_components_90} / {len(continuous_cols)}")
    print(f"   - 数据降维效果: 显著（维度压缩比 {n_components_80/len(continuous_cols):.2f}）")

print("\n5. 机器学习模型性能:")
if 'y_pred' in locals():
    from sklearn.metrics import accuracy_score
    accuracy = accuracy_score(y_test, y_pred)
    print(f"   - 离散变量预测准确率: {accuracy:.3f}")
if 'r2' in locals():
    print(f"   - 时间序列预测R²: {r2:.3f}")
    print(f"   - 时间序列预测RMSE: {rmse:.4f}")

print("\n6. 因果关系发现:")
if 'significant_causality' in locals():
    print(f"   - 显著格兰杰因果关系数量: {len(significant_causality)}")
    if len(significant_causality) > 0:
        print("   - 主要因果关系:")
        for _, row in significant_causality.head(3).iterrows():
            print(f"     变量{row['原因变量']} → 变量{row['结果变量']} (p={row['p值']:.4f})")