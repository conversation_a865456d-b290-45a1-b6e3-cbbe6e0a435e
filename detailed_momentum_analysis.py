#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的动量效应分析报告
包含交易信号详情和深度分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """加载验证数据"""
    df = pd.read_excel('/Users/<USER>/Desktop/验证.xlsx')
    df.columns = ['日期', '国证2000', '万得等权重指数', '转债对应正股指数']
    df['日期'] = pd.to_datetime(df['日期'])
    df.set_index('日期', inplace=True)
    df = df.dropna()
    return df

def calculate_momentum_signal(price_series, lookback_period=20):
    """计算动量信号"""
    returns = price_series.pct_change()
    momentum = returns.rolling(window=lookback_period).sum()
    signal = (momentum > 0).astype(int)
    return signal, momentum

def calculate_strategy_returns(price_series, signal):
    """计算策略收益率"""
    daily_returns = price_series.pct_change()
    strategy_returns = signal.shift(1) * daily_returns
    cumulative_returns = (1 + strategy_returns.fillna(0)).cumprod()
    return strategy_returns, cumulative_returns

def generate_detailed_report():
    """生成详细分析报告"""
    output_dir = '/Users/<USER>/Desktop/动量验证'
    
    # 加载数据
    df = load_data()
    
    # 计算所有指数的信号和收益
    results = {}
    signals_df = pd.DataFrame(index=df.index)
    
    for column in df.columns:
        signal, momentum = calculate_momentum_signal(df[column], 20)
        strategy_returns, cumulative_returns = calculate_strategy_returns(df[column], signal)
        
        signals_df[f'{column}_信号'] = signal
        signals_df[f'{column}_动量'] = momentum
        signals_df[f'{column}_策略收益'] = strategy_returns
        signals_df[f'{column}_累计净值'] = cumulative_returns
        
        results[column] = {
            'signal': signal,
            'momentum': momentum,
            'strategy_returns': strategy_returns,
            'cumulative_returns': cumulative_returns
        }
    
    # 保存详细交易信号
    signals_path = os.path.join(output_dir, '详细交易信号.xlsx')
    signals_df.to_excel(signals_path)
    
    # 分析交易频率
    print("=== 交易频率分析 ===")
    for column in df.columns:
        signal = results[column]['signal']
        signal_changes = signal.diff().abs().sum()
        total_days = len(signal.dropna())
        trade_frequency = signal_changes / total_days * 100
        
        holding_days = signal.sum()
        holding_ratio = holding_days / total_days * 100
        
        print(f"{column}:")
        print(f"  交易频率: {trade_frequency:.1f}% (信号变化次数/总天数)")
        print(f"  持仓比例: {holding_ratio:.1f}% (持仓天数/总天数)")
        print()
    
    # 分析不同市场环境下的表现
    print("=== 不同市场环境表现分析 ===")
    
    # 按年份分析
    yearly_performance = {}
    for year in range(2018, 2026):
        year_data = df[df.index.year == year]
        if len(year_data) == 0:
            continue
            
        print(f"\n{year}年表现:")
        yearly_performance[year] = {}
        
        for column in df.columns:
            year_signal = results[column]['signal'][results[column]['signal'].index.year == year]
            year_returns = results[column]['strategy_returns'][results[column]['strategy_returns'].index.year == year]
            
            if len(year_returns.dropna()) > 0:
                year_total_return = (1 + year_returns.fillna(0)).prod() - 1
                yearly_performance[year][column] = year_total_return
                print(f"  {column}: {year_total_return:.2%}")
    
    # 创建年度表现对比图
    plt.figure(figsize=(14, 8))
    
    years = list(yearly_performance.keys())
    indices = df.columns
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    x = np.arange(len(years))
    width = 0.25
    
    for i, index in enumerate(indices):
        yearly_returns = [yearly_performance[year].get(index, 0) for year in years]
        plt.bar(x + i*width, [r*100 for r in yearly_returns], width, 
                label=index, color=colors[i], alpha=0.8)
    
    plt.xlabel('年份', fontsize=12)
    plt.ylabel('年度收益率 (%)', fontsize=12)
    plt.title('各指数动量策略年度表现对比', fontsize=16, fontweight='bold')
    plt.xticks(x + width, years)
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.tight_layout()
    
    plt.savefig(os.path.join(output_dir, '年度表现对比图.png'), 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    return results, signals_df

def analyze_momentum_persistence():
    """分析动量持续性"""
    df = load_data()
    output_dir = '/Users/<USER>/Desktop/动量验证'
    
    print("=== 动量持续性分析 ===")
    
    persistence_results = {}
    
    for column in df.columns:
        returns = df[column].pct_change()
        
        # 计算不同期间的动量
        momentum_periods = [5, 10, 20, 30, 60]
        correlations = []
        
        for period in momentum_periods:
            momentum = returns.rolling(window=period).sum()
            future_returns = returns.shift(-period).rolling(window=period).sum()
            
            correlation = momentum.corr(future_returns)
            correlations.append(correlation)
        
        persistence_results[column] = correlations
        
        print(f"{column} 动量持续性 (与未来收益相关性):")
        for period, corr in zip(momentum_periods, correlations):
            print(f"  {period}日动量: {corr:.3f}")
        print()
    
    # 绘制动量持续性图
    plt.figure(figsize=(12, 8))
    
    momentum_periods = [5, 10, 20, 30, 60]
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    for i, (column, correlations) in enumerate(persistence_results.items()):
        plt.plot(momentum_periods, correlations, marker='o', linewidth=2, 
                label=column, color=colors[i])
    
    plt.xlabel('动量计算期间 (交易日)', fontsize=12)
    plt.ylabel('与未来收益相关性', fontsize=12)
    plt.title('各指数动量效应持续性对比', fontsize=16, fontweight='bold')
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.tight_layout()
    
    plt.savefig(os.path.join(output_dir, '动量持续性分析图.png'), 
                dpi=300, bbox_inches='tight')
    plt.show()
    
    return persistence_results

if __name__ == "__main__":
    print("开始详细动量效应分析...")
    
    # 生成详细报告
    results, signals_df = generate_detailed_report()
    
    # 分析动量持续性
    persistence_results = analyze_momentum_persistence()
    
    print("\n详细分析完成！")
    print("生成的文件包括：")
    print("1. 详细交易信号.xlsx - 包含所有交易信号和收益数据")
    print("2. 年度表现对比图.png - 各年度策略表现对比")
    print("3. 动量持续性分析图.png - 动量效应持续性分析")
