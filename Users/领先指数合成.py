import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

FILE_PATH = "/Users/<USER>/Desktop/周频指数合成0804.xlsx"
OUTPUT_FILE_PATH = "/Users/<USER>/Desktop/利率领先指数_分层结果.xlsx"
TARGET_VARIABLE = "10Y国债收益率"

CATEGORIES = {
    "工业上游生产": ["中国:高炉开工率(247家)", "石油沥青装置开工率", "中国:开工率:螺纹钢:主要钢厂"],
    "生产生活资料价格": ["南华工业品指数", "CRB现货指数:综合", "秦皇岛港:平仓价:动力末煤", "中国:平均批发价:猪肉", "建材综合指数"],
    "地产与建筑": ["中国:100大中城市:成交土地溢价率", "中国:30大中城市:成交面积:商品房", "二手房挂牌指数", "中国:100大中城市:成交土地占地面积"],
    "耐用消费品": ["中国:日均销量(当周,厂家零售):乘用车", "主要家电零售额", "中国:日均销量(当周,厂家批发):乘用车"],
    "就业预期": ["互联网搜索指数:失业金领取条件"],
    "交通运输": ["地铁客运量"],
    "出口": ["中国出口集装箱运价指数:综合指数"],
    "流动性与政策预期": ["DR007", "利率互换:FR007:1年", "利率互换:FR007:5年", "R007", "逆回购利率:7天"],
    "风险偏好与资产比价": ["AAA企业债-10Y国债", "1/沪深300PE（TTM）", "美元指数"],
}

PROCESSING_RULES = {
    "中国:高炉开工率(247家)": {"seasonal": True, "transform": "level"},
    "石油沥青装置开工率": {"seasonal": True, "transform": "level"},
    "中国:开工率:螺纹钢:主要钢厂": {"seasonal": True, "transform": "level"},
    "南华工业品指数": {"seasonal": False, "transform": "yoy_52"},
    "CRB现货指数:综合": {"seasonal": False, "transform": "yoy_52"},
    "秦皇岛港:平仓价:动力末煤": {"seasonal": False, "transform": "yoy_52"},
    "中国:平均批发价:猪肉": {"seasonal": False, "transform": "yoy_52"},
    "建材综合指数": {"seasonal": False, "transform": "yoy_52"},
    "中国:100大中城市:成交土地溢价率": {"seasonal": False, "transform": "ma_4"},
    "中国:30大中城市:成交面积:商品房": {"seasonal": True, "transform": "yoy_52"},
    "二手房挂牌指数": {"seasonal": False, "transform": "pct_change_13"},
    "中国:100大中城市:成交土地占地面积": {"seasonal": True, "transform": "yoy_52"},
    "中国:日均销量(当周,厂家零售):乘用车": {"seasonal": True, "transform": "yoy_52"},
    "主要家电零售额": {"seasonal": True, "transform": "yoy_52"},
    "中国:日均销量(当周,厂家批发):乘用车": {"seasonal": True, "transform": "yoy_52"},
    "互联网搜索指数:失业金领取条件": {"seasonal": False, "transform": "level"},
    "地铁客运量": {"seasonal": True, "transform": "level"},
    "中国出口集装箱运价指数:综合指数": {"seasonal": False, "transform": "yoy_52"},
    "DR007": {"seasonal": False, "transform": "level"},
    "利率互换:FR007:1年": {"seasonal": False, "transform": "level"},
    "利率互换:FR007:5年": {"seasonal": False, "transform": "level"},
    "R007": {"seasonal": False, "transform": "level"},
    "逆回购利率:7天": {"seasonal": False, "transform": "level"},
    "AAA企业债-10Y国债": {"seasonal": False, "transform": "level", "direction": -1},
    "1/沪深300PE（TTM）": {"seasonal": False, "transform": "level"},
    "美元指数": {"seasonal": False, "transform": "level"},
}


def load_data(file_path: str) -> pd.DataFrame | None:
    try:
        df = pd.read_excel(file_path, sheet_name="总表", header=0)
    except FileNotFoundError:
        print(f"[错误] 文件不存在：{file_path}")
        return None
    df = df.rename(columns={df.columns[0]: "Date"})
    df["Date"] = pd.to_datetime(df["Date"])
    df = df.set_index("Date")
    if df.index.duplicated().any():
        df = df[~df.index.duplicated(keep="last")]
    df = df.ffill()
    print(f"[数据] 形状={df.shape}, 区间={df.index.min().date()} ~ {df.index.max().date()}")
    return df


def process_indicator(series: pd.Series, rule: dict) -> pd.Series:
    s = series.copy()
    if rule.get("seasonal", False):
        s = s - s.shift(52)
    t = rule.get("transform")
    if t == "yoy_52":
        s = s.pct_change(52) * 100
    elif t == "pct_change_13":
        s = s.pct_change(13) * 100
    elif t == "ma_4":
        s = s.rolling(4, min_periods=1).mean()
    s = s * rule.get("direction", 1)
    s = s.replace([np.inf, -np.inf], np.nan).dropna()
    if s.size < 10:
        return pd.Series(dtype=float, name=series.name)
    scaler = StandardScaler()
    s = pd.Series(
        scaler.fit_transform(s.values.reshape(-1, 1)).ravel(),
        index=s.index,
        name=series.name,
    )
    return s


def zscore_df(df: pd.DataFrame) -> pd.DataFrame:
    out = pd.DataFrame(index=df.index)
    for c in df.columns:
        col = df[c]
        mu = col.mean(skipna=True)
        sig = col.std(ddof=0, skipna=True)
        out[c] = (col - mu) / sig if sig and not np.isclose(sig, 0.0) else np.nan
    return out


def main():
    print("[开始] 利率综合领先指数构建")
    plt.rcParams["font.sans-serif"] = ["PingFang SC", "Hiragino Sans GB", "Arial Unicode MS", "SimHei"]
    plt.rcParams["axes.unicode_minus"] = False

    df_raw = load_data(FILE_PATH)
    if df_raw is None:
        return

    processed_by_cat: dict[str, pd.DataFrame] = {}
    print("[处理] 指标清洗与标准化")
    for cat, indicators in CATEGORIES.items():
        cat_df = pd.DataFrame(index=df_raw.index)
        for ind in indicators:
            if ind not in df_raw.columns:
                print(f"[警告] 未找到指标列：{ind}")
                continue
            rule = PROCESSING_RULES.get(ind)
            if not rule:
                print(f"[警告] 未定义处理规则：{ind}")
                continue
            s = process_indicator(df_raw[ind], rule)
            if not s.empty:
                cat_df[ind] = s.reindex(df_raw.index).ffill()
        processed_by_cat[cat] = cat_df.dropna(how="all")

    print("[合成] 9大分类指数")
    category_indices = pd.DataFrame(index=df_raw.index)
    for cat, cat_df in processed_by_cat.items():
        if cat_df.empty:
            continue
        valid = cat_df.dropna(how="any")
        if valid.shape[1] > 1 and valid.shape[0] >= 10:
            pca = PCA(n_components=1)
            comp = pca.fit_transform(valid.values).ravel()
            corrs = [np.corrcoef(comp, valid[c])[0, 1] for c in valid.columns]
            if np.nanmean(corrs) < 0:
                comp = -comp
            s = pd.Series(comp, index=valid.index, name=cat)
        else:
            if valid.shape[1] == 0:
                continue
            s = valid.iloc[:, 0].copy() if valid.shape[1] == 1 else valid.mean(axis=1)
            s.name = cat
        category_indices[cat] = s.reindex(df_raw.index).ffill()

    category_indices = zscore_df(category_indices)

    print("[聚合] 2大分项指数")
    fundamental_cats = [c for c in CATEGORIES if c not in ["流动性与政策预期", "风险偏好与资产比价"]]
    funding_cats = ["流动性与政策预期", "风险偏好与资产比价"]
    exist_fundamental = [c for c in fundamental_cats if c in category_indices.columns]
    exist_funding = [c for c in funding_cats if c in category_indices.columns]
    main_indices = pd.DataFrame(index=category_indices.index)
    if exist_fundamental:
        main_indices["基本面分项指数"] = category_indices[exist_fundamental].mean(axis=1)
    if exist_funding:
        main_indices["资金与情绪分项指数"] = category_indices[exist_funding].mean(axis=1)

    print("[合成] 最终综合指数")
    final_index = pd.DataFrame(index=category_indices.index)
    final_index["利率综合领先指数"] = main_indices.mean(axis=1)

    print("[绘图] 指数走势")
    try:
        fig1, axes1 = plt.subplots(3, 3, figsize=(20, 15))
        fig1.suptitle("9大分类指数走势", fontsize=18)
        axes1 = axes1.flatten()
        cols = list(category_indices.columns)
        for i, cat in enumerate(cols[:9]):
            axes1[i].plot(category_indices[cat])
            axes1[i].set_title(cat)
            axes1[i].grid(True)
            axes1[i].tick_params(axis="x", rotation=30)
        for j in range(len(cols), 9):
            axes1[j].axis("off")
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])
    except Exception as e:
        print(f"[提示] 分类指数绘图跳过：{e}")

    if "基本面分项指数" in main_indices.columns:
        fig2, ax2 = plt.subplots(figsize=(15, 8))
        ax2.plot(main_indices["基本面分项指数"], label="基本面分项指数", linewidth=2.2)
        for cat in exist_fundamental:
            ax2.plot(category_indices[cat], label=cat, alpha=0.5)
        ax2.set_title("基本面分项指数（含分类）")
        ax2.legend(loc="upper left", bbox_to_anchor=(1, 1))
        ax2.grid(True)
        fig2.tight_layout()

    if "资金与情绪分项指数" in main_indices.columns:
        fig3, ax3 = plt.subplots(figsize=(15, 8))
        ax3.plot(main_indices["资金与情绪分项指数"], label="资金与情绪分项指数", linewidth=2.2)
        for cat in exist_funding:
            ax3.plot(category_indices[cat], label=cat, alpha=0.6)
        ax3.set_title("资金与情绪分项指数（含分类）")
        ax3.legend(loc="upper left", bbox_to_anchor=(1, 1))
        ax3.grid(True)
        fig3.tight_layout()

    if {"基本面分项指数", "资金与情绪分项指数"}.issubset(main_indices.columns):
        fig4, ax4 = plt.subplots(figsize=(15, 8))
        ax4.plot(main_indices["基本面分项指数"], label="基本面分项指数", linewidth=2)
        ax4.plot(main_indices["资金与情绪分项指数"], label="资金与情绪分项指数", linewidth=2)
        ax4.set_title("两大分项指数对比")
        ax4.legend()
        ax4.grid(True)

    if TARGET_VARIABLE in df_raw.columns:
        df_final = pd.concat([final_index, df_raw[[TARGET_VARIABLE]]], axis=1).dropna()
        scaler_final = StandardScaler()
        df_final_scaled = pd.DataFrame(
            scaler_final.fit_transform(df_final.values),
            index=df_final.index,
            columns=df_final.columns,
        )
        fig5, ax5 = plt.subplots(figsize=(15, 8))
        ax5.plot(df_final_scaled["利率综合领先指数"], label="利率综合领先指数（标准化）", linewidth=2)
        ax5.plot(df_final_scaled[TARGET_VARIABLE], label=f"{TARGET_VARIABLE}（标准化）", alpha=0.75)
        ax5.set_title("领先指数 vs 10Y国债收益率")
        ax5.legend()
        ax5.grid(True)
    else:
        print(f"[提示] 源数据缺少目标列：{TARGET_VARIABLE}，跳过对比图。")

    plt.show()

    print(f"[导出] 写入：{OUTPUT_FILE_PATH}")
    try:
        with pd.ExcelWriter(OUTPUT_FILE_PATH) as writer:
            category_indices.to_excel(writer, sheet_name="9大分类指数")
            main_indices.to_excel(writer, sheet_name="2大分项指数")
            if TARGET_VARIABLE in df_raw.columns:
                pd.concat([final_index, df_raw[[TARGET_VARIABLE]]], axis=1).to_excel(
                    writer, sheet_name="最终综合指数与原始利率"
                )
            else:
                final_index.to_excel(writer, sheet_name="最终综合指数")
        print("[完成] 文件导出成功")
    except Exception as e:
        print(f"[错误] 导出失败：{e}")


if __name__ == "__main__":
    main()