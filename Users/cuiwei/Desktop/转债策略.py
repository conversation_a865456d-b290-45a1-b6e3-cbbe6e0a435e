#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import warnings
from pathlib import Path
from datetime import timedelta

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

warnings.filterwarnings("ignore")
plt.rcParams["font.sans-serif"] = ["Arial Unicode MS", "SimHei", "DejaVu Sans"]
plt.rcParams["axes.unicode_minus"] = False


class RuleCBStrategyFourFactors:
    def __init__(self,
                 cb_path="/Users/<USER>/Desktop/转债数据.xlsx",
                 bench_path="/Users/<USER>/Desktop/中证指数行情.xlsx",
                 out_dir="/Users/<USER>/Desktop/转债_四因子规则回测",
                 start="2019-01-05", end="2025-08-15",
                 hold_n=30, fee_roundtrip=0.003):
        self.cb_path = cb_path
        self.bench_path = bench_path
        self.out_dir = Path(out_dir); self.out_dir.mkdir(parents=True, exist_ok=True)
        self.start = pd.to_datetime(start); self.end = pd.to_datetime(end)
        self.hold_n = int(hold_n)
        self.fee_roundtrip = float(fee_roundtrip)
        self.fee_side = self.fee_roundtrip / 2.0
        self.allowed_ratings = {"AAA", "AA+", "AA", "AA-"}
        self.cb = None
        self.bench = None
        self.ret_matrix = None
        self.weekly_logs = []
        self.hold_logs = []

    # -------------------- 数据与基础字段 --------------------
    def load_data(self):
        cb = pd.read_excel(self.cb_path, sheet_name=0)
        cb.columns = cb.columns.str.strip()
        cb["交易日期"] = pd.to_datetime(cb["交易日期"])
        cb = cb.sort_values(["转债代码", "交易日期"]).reset_index(drop=True)

        num_cols = ["收盘价","最高价","最低价","成交量","换手率","振幅","转债余额","剩余期限",
                    "转换价值","纯债价值","转股价","转股溢价率","纯债溢价率","双低","正股收盘价","剩余市值"]
        for c in num_cols:
            if c in cb.columns:
                cb[c] = pd.to_numeric(cb[c], errors="coerce")

        cb = cb[(cb["交易日期"] >= self.start - timedelta(days=450)) & (cb["交易日期"] <= self.end)].copy()
        cb["转债日收益率"] = cb.groupby("转债代码")["收盘价"].pct_change().fillna(0)
        cb["正股日收益率"] = cb.groupby("转债代码")["正股收盘价"].pct_change().fillna(0)
        cb["上市天数"] = cb.groupby("转债代码").cumcount()
        cb["日成交金额"] = cb["成交量"] * cb["收盘价"]

        if "转股溢价率" not in cb or cb["转股溢价率"].isna().all():
            if {"收盘价","转换价值"}.issubset(cb.columns):
                cb["转股溢价率"] = (cb["收盘价"] / cb["转换价值"] - 1).replace([np.inf,-np.inf], np.nan)
        if "纯债溢价率" not in cb or cb["纯债溢价率"].isna().all():
            if {"收盘价","纯债价值"}.issubset(cb.columns):
                cb["纯债溢价率"] = (cb["收盘价"] / cb["纯债价值"] - 1).replace([np.inf,-np.inf], np.nan)

        if "剩余市值" not in cb.columns or cb["剩余市值"].isna().all():
            if {"转债余额","收盘价"}.issubset(cb.columns):
                cb["剩余市值"] = (cb["转债余额"] / 100.0) * cb["收盘价"]

        bench = pd.read_excel(self.bench_path, sheet_name=0)
        bench.columns = bench.columns.str.strip()
        bench = bench.iloc[:, -2:]
        bench.columns = ["日期","收盘价"]
        bench["日期"] = pd.to_datetime(bench["日期"])
        bench = bench.sort_values("日期").reset_index(drop=True)
        bench = bench[(bench["日期"] >= self.start) & (bench["日期"] <= self.end)].copy()

        self.cb = cb
        self.bench = bench
        self.ret_matrix = self.cb.pivot(index="交易日期", columns="转债代码", values="转债日收益率").sort_index()

    # -------------------- 四个基础因子 --------------------
    @staticmethod
    def _linreg_quality(y: np.ndarray) -> float:
        n = len(y)
        if n < 3 or np.allclose(y.std(), 0):
            return 0.0
        x = np.arange(n, dtype=float)
        sx, sy = x.sum(), y.sum()
        sxx, sxy = (x*x).sum(), (x*y).sum()
        denom = n*sxx - sx*sx
        if denom == 0:
            return 0.0
        slope = (n*sxy - sx*sy) / denom
        r = np.corrcoef(x, y)[0, 1]
        r2 = 0.0 if np.isnan(r) else r*r
        return float(slope * r2)

    def _compute_factors(self):
        g = self.cb.groupby("转债代码", group_keys=False)

        win_sharpe = g["转债日收益率"].rolling(20, min_periods=10).mean() / (
            g["转债日收益率"].rolling(20, min_periods=10).std() + 1e-8
        )
        pos_ratio = g["转债日收益率"].rolling(20, min_periods=10).apply(lambda a: np.mean(a > 0), raw=True)
        self.cb["Alpha动量稳定性"] = (win_sharpe * pos_ratio).reset_index(level=0, drop=True)

        logp = np.log(self.cb["收盘价"].clip(lower=1))
        tq = g.apply(lambda df: df.assign(_tq=logp.loc[df.index]
                                          .rolling(60, min_periods=40)
                                          .apply(lambda s: self._linreg_quality(s.values), raw=False))["_tq"])
        self.cb["趋势质量因子_60D"] = tq.reset_index(level=0, drop=True)

        mom5 = g["收盘价"].pct_change(5)
        mom5_prev = mom5.groupby(self.cb["转债代码"]).shift(5)
        self.cb["动量加速度_5D"] = (mom5 - mom5_prev).replace([np.inf,-np.inf], np.nan)

        stock_vol = g["正股日收益率"].rolling(60, min_periods=40).std() * np.sqrt(252)
        self.cb["正股年化波动率"] = stock_vol.reset_index(level=0, drop=True)

        ma5 = g["收盘价"].rolling(5, min_periods=3).mean().reset_index(level=0, drop=True)
        self.cb["乖离率5"] = (self.cb["收盘价"] / ma5 - 1).abs().replace([np.inf,-np.inf], np.nan)

        for c in ["Alpha动量稳定性", "趋势质量因子_60D", "动量加速度_5D", "正股年化波动率"]:
            self.cb[f"{c}_pct"] = self.cb.groupby("交易日期")[c].transform(lambda s: s.rank(pct=True))

    # -------------------- 选股与过滤 --------------------
    def _weekly_rebalance_days(self):
        dates = pd.Index(sorted(self.cb[(self.cb["交易日期"]>=self.start)&(self.cb["交易日期"]<=self.end)]["交易日期"].unique()))
        fri = dates[dates.weekday == 5 - 1]  # 周五
        if len(fri) == 0:
            fri = dates[::5]
        return fri

    def _universe_filter(self, x: pd.DataFrame) -> pd.DataFrame:
        x = x.copy()
        cond = (
            (x["上市天数"] >= 60) &
            (x.get("隐含评级", "").isin(self.allowed_ratings) if "隐含评级" in x.columns else True) &
            (x["收盘价"].between(100, 200, inclusive="both")) &
            (x["剩余期限"].fillna(0) >= 1.0) &
            (x["乖离率5"].fillna(0) <= 0.06) &
            (x["剩余市值"].fillna(0) >= 3e8)
        )
        last_ret = x["转债日收益率"].fillna(0).abs() <= 0.10
        call_ok = (x.get("强赎剩余计数", pd.Series(np.inf, index=x.index)).fillna(np.inf) >= 6)
        liq_ok = x["成交量"].fillna(0) > 0
        x = x[cond & last_ret & call_ok & liq_ok]
        return x

    def _score(self, x: pd.DataFrame) -> pd.Series:
        w = dict(alpha=0.40, trend=0.35, accel=0.15, stockvol=0.10)
        s = (
            w["alpha"] * x["Alpha动量稳定性_pct"].fillna(0.5) +
            w["trend"] * x["趋势质量因子_60D_pct"].fillna(0.5) +
            w["accel"] * x["动量加速度_5D_pct"].fillna(0.5) +
            w["stockvol"] * (1 - x["正股年化波动率_pct"].fillna(0.5))
        )
        return s

    # -------------------- 回测引擎 --------------------
    @staticmethod
    def _turnover(old_w: pd.Series, new_w: pd.Series) -> float:
        z = pd.concat([old_w, new_w], axis=1).fillna(0.0)
        z.columns = ["old","new"]
        return float(np.abs(z["new"] - z["old"]).sum() / 2.0)

    def _portfolio_week_return(self, codes, t0, t1):
        sub = self.ret_matrix.loc[(self.ret_matrix.index > t0) & (self.ret_matrix.index <= t1), codes]
        if sub.empty:
            return 0.0
        daily = sub.mean(axis=1).fillna(0)
        return float((1 + daily).prod() - 1)

    def backtest(self):
        self._compute_factors()
        rbd = self._weekly_rebalance_days()
        if len(rbd) < 2:
            raise RuntimeError("调仓日期不足")

        cur_weights = pd.Series(dtype=float)
        weekly_ret, bench_ret, dates_nav = [], [], []

        for i in range(len(rbd)-1):
            t0, t1 = rbd[i], rbd[i+1]
            cs = self.cb[self.cb["交易日期"] == t0].copy()
            cs = self._universe_filter(cs)
            need = ["Alpha动量稳定性_pct","趋势质量因子_60D_pct","动量加速度_5D_pct","正股年化波动率_pct"]
            if cs.empty or not set(need).issubset(cs.columns):
                continue

            cs = cs.assign(综合得分=self._score(cs)).sort_values("综合得分", ascending=False)
            n_pick = min(self.hold_n, len(cs))
            pick = cs.head(n_pick).copy()
            new_weights = pd.Series(1.0/n_pick, index=pick["转债代码"])

            to = self._turnover(cur_weights, new_weights)
            cost = to * (self.fee_roundtrip)

            wr = self._portfolio_week_return(pick["转债代码"].tolist(), t0, t1) - cost
            weekly_ret.append(wr)

            b_sub = self.bench[(self.bench["日期"] > t0) & (self.bench["日期"] <= t1)]
            b_ret = float(b_sub["收盘价"].iloc[-1] / b_sub["收盘价"].iloc[0] - 1) if len(b_sub) >= 2 else 0.0
            bench_ret.append(b_ret)
            dates_nav.append(t1)

            cur_weights = new_weights
            hold_cols = ["转债代码","转债简称","收盘价","Alpha动量稳定性_pct","趋势质量因子_60D_pct","动量加速度_5D_pct","正股年化波动率_pct","综合得分"]
            hold_cols = [c for c in hold_cols if c in pick.columns]
            tmp = pick[hold_cols].copy(); tmp["调仓日期"] = t0
            self.hold_logs.append(tmp)
            self.weekly_logs.append({"开始":t0,"结束":t1,"组合收益":wr,"基准收益":b_ret,"换手率":to,"成本":cost,"持仓数":n_pick})

        res = pd.DataFrame({"日期": dates_nav, "组合周收益": weekly_ret, "基准周收益": bench_ret}).set_index("日期")
        res["策略净值"] = (1 + res["组合周收益"]).cumprod()
        res["基准净值"] = (1 + res["基准周收益"]).cumprod()
        self.bt_res = res
        return res

    # -------------------- 绩效与输出 --------------------
    @staticmethod
    def _mdd(series: pd.Series):
        rollmax = series.cummax()
        dd = series/rollmax - 1.0
        mdd = dd.min()
        end = dd.idxmin()
        start = series.loc[:end].idxmax()
        return float(mdd), start, end

    def performance(self):
        s = self.bt_res["组合周收益"].fillna(0)
        b = self.bt_res["基准周收益"].fillna(0)
        nav = self.bt_res["策略净值"]

        ann_ret = (1 + s.mean()) ** 52 - 1
        vol = s.std(ddof=0) * np.sqrt(52)
        sharpe = ann_ret / vol if vol > 0 else 0.0
        mdd, dd_s, dd_e = self._mdd(nav)
        calmar = ann_ret / abs(mdd) if mdd < 0 else np.nan
        win = (s > 0).mean()

        excess = s - b
        ir = (excess.mean() / excess.std(ddof=0) * np.sqrt(52)) if excess.std(ddof=0) > 0 else 0.0

        perf = pd.Series({
            "样本周数": len(s),
            "年化收益": ann_ret,
            "年化波动": vol,
            "夏普": sharpe,
            "最大回撤": mdd,
            "回撤开始": dd_s,
            "回撤结束": dd_e,
            "卡玛比率": calmar,
            "胜率": win,
            "信息比率": ir
        })
        return perf

    def save_outputs(self):
        out_xlsx = self.out_dir / "四因子规则回测.xlsx"
        wl = pd.DataFrame(self.weekly_logs)
        holdings = pd.concat(self.hold_logs, ignore_index=True) if self.hold_logs else pd.DataFrame()

        with pd.ExcelWriter(out_xlsx, engine="openpyxl") as writer:
            self.bt_res.reset_index().to_excel(writer, sheet_name="周度净值", index=False)
            self.performance().to_frame("指标").to_excel(writer, sheet_name="绩效指标")
            wl.to_excel(writer, sheet_name="周度明细", index=False)
            if not holdings.empty:
                holdings.to_excel(writer, sheet_name="调仓持仓", index=False)

        fig, ax = plt.subplots(figsize=(12,6))
        self.bt_res[["策略净值","基准净值"]].plot(ax=ax, linewidth=1.6)
        nav = self.bt_res["策略净值"]
        dd = nav/nav.cummax() - 1.0
        ax2 = ax.twinx()
        ax2.fill_between(self.bt_res.index, dd, 0, color="gray", alpha=0.2, step="pre")
        ax.set_title("策略与基准净值（含回撤阴影）"); ax.grid(True, linestyle="--", alpha=0.3)
        ax2.set_ylabel("回撤")
        plt.tight_layout()
        plt.savefig(self.out_dir / "净值_回撤.png", dpi=200); plt.close(fig)

        fig, ax = plt.subplots(figsize=(10,5))
        bins = np.histogram_bin_edges(np.r_[self.bt_res["组合周收益"].values, self.bt_res["基准周收益"].values], bins="auto")
        ax.hist(self.bt_res["组合周收益"].values, bins=bins, alpha=0.6, label="策略")
        ax.hist(self.bt_res["基准周收益"].values, bins=bins, alpha=0.6, label="基准")
        ax.set_title("周收益分布"); ax.legend(); ax.grid(True, linestyle="--", alpha=0.3)
        plt.tight_layout()
        plt.savefig(self.out_dir / "周收益分布.png", dpi=200); plt.close(fig)

    def run(self):
        self.load_data()
        self.backtest()
        perf = self.performance()
        self.save_outputs()
        print(perf.round(4))


if __name__ == "__main__":
    strategy = RuleCBStrategyFourFactors(
        cb_path="/Users/<USER>/Desktop/转债数据.xlsx",
        bench_path="/Users/<USER>/Desktop/中证指数行情.xlsx",
        out_dir="/Users/<USER>/Desktop/转债_四因子规则回测",
        start="2019-01-05",
        end="2025-08-15",
        hold_n=30,
        fee_roundtrip=0.003
    )
    strategy.run()