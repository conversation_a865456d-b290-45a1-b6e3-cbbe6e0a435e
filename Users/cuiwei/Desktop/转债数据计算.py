#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
from pathlib import Path

import numpy as np
import pandas as pd
from scipy.integrate import quad
from scipy.stats import norm

import warnings
warnings.filterwarnings("ignore")


RF_RATE = 0.03
CALL_COEF = 1.30
PROTECT_YEARS = 2.0


def efficient_data_merge() -> pd.DataFrame:
    file_path = os.path.expanduser("~/Desktop/转债量化.xlsx")
    sheets = {
        "债券收盘价": "债券收盘价",
        "债券均价": "债券均价",
        "正股收盘价": "正股收盘价",
        "正股均价": "正股均价",
        "剩余期限": "剩余期限",
        "转股价": "转股价",
        "隐含波动率": "隐含波动率",
        "纯债价值": "纯债价值",
        "转换价值": "转换价值",
    }

    dfs = []
    for sheet_name, col_name in sheets.items():
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=0)
        df.rename(columns={df.columns[0]: "交易日期"}, inplace=True)
        df["交易日期"] = pd.to_datetime(df["交易日期"], errors="coerce")
        df = df.dropna(subset=["交易日期"]).set_index("交易日期").sort_index()
        df_long = df.stack(dropna=True).reset_index()
        df_long.columns = ["交易日期", "证券代码", col_name]
        dfs.append(df_long)

    out = dfs[0]
    for df in dfs[1:]:
        out = out.merge(df, on=["交易日期", "证券代码"], how="outer")

    out["证券代码"] = out["证券代码"].astype(str)
    out = out.dropna(how="all", subset=list(sheets.values()))
    out["证券简称"] = out["证券代码"].str.split(".").str[0]
    cols = ["证券代码", "证券简称", "交易日期"] + list(sheets.values())
    out = out[cols].sort_values(["交易日期", "证券代码"]).reset_index(drop=True)

    # 上市距今年：使用首日上市日期计算，更稳健
    first_day = out.groupby("证券代码")["交易日期"].transform("min")
    out["上市距今年"] = (out["交易日期"] - first_day).dt.days / 365.25

    return out


def _d_pm(S0: float, K: float, rf: float, sigma: float, tau: float, sign: int = +1) -> float:
    if tau <= 0 or sigma <= 0 or S0 <= 0 or K <= 0:
        return 0.0
    num = np.log(S0 / K) + (rf + 0.5 * sigma * sigma) * tau
    den = sigma * np.sqrt(tau)
    return num / den - (den if sign < 0 else 0.0)


def _first_hit_pdf(t: float, S0: float, C: float, mu: float, sigma: float) -> float:
    if t <= 0 or sigma <= 0 or S0 <= 0 or C <= 0:
        return 0.0
    ln_ratio = np.log(C / S0)
    return ln_ratio * np.exp(-(ln_ratio - mu * t) ** 2 / (2 * sigma * sigma * t)) / (
        sigma * np.sqrt(2 * np.pi * t**3)
    )


def equity_path_A1(S0: float, C: float, rf: float, sigma: float, tp: float) -> float:
    if tp <= 0 or sigma <= 0:
        return 0.0
    d1 = _d_pm(S0, C, rf, sigma, tp, +1)
    return S0 * norm.cdf(d1)


def equity_path_A2(S0: float, C: float, rf: float, sigma: float, tp: float, T: float) -> float:
    if T <= tp or sigma <= 0:
        return 0.0
    mu = rf - 0.5 * sigma * sigma

    def integrand(t):
        return np.exp(-rf * t) * _first_hit_pdf(t, S0, C, mu, sigma)

    try:
        val, _ = quad(integrand, tp, T, epsrel=1e-6, limit=200)
        return C * val
    except Exception:
        return 0.0


def equity_path_A3(S0: float, C: float, K_eff: float, rf: float, sigma: float, tp: float, T: float) -> float:
    tau = T - tp
    if tau <= 0 or sigma <= 0 or S0 <= 0 or K_eff <= 0:
        return 0.0
    lam = (rf + 0.5 * sigma * sigma) / (sigma * sigma)
    d1 = _d_pm(S0, K_eff, rf, sigma, tau, +1)
    e1 = (np.log((C * C) / (S0 * K_eff)) + (rf + 0.5 * sigma * sigma) * tau) / (sigma * np.sqrt(tau))
    return S0 * (norm.cdf(d1) - (C / S0) ** (2 * lam) * norm.cdf(e1))


def ccb_equity_value(row: pd.Series) -> float:
    S0 = row.get("转换价值")
    sigma = row.get("隐含波动率")
    T = row.get("剩余期限")
    conv_px = row.get("转股价")
    bond_val = row.get("纯债价值")
    if any(pd.isna(x) for x in [S0, sigma, T, conv_px, bond_val]):
        return np.nan
    if S0 <= 0 or sigma <= 0 or T <= 0 or conv_px <= 0 or bond_val <= 0:
        return np.nan

    C = CALL_COEF * conv_px
    K_eff = bond_val
    yrs = row.get("上市距今年")
    yrs = 0.0 if pd.isna(yrs) else float(max(0.0, yrs))
    tp = max(0.0, PROTECT_YEARS - yrs)

    a1 = equity_path_A1(S0, C, RF_RATE, sigma, tp)
    a2 = equity_path_A2(S0, C, RF_RATE, sigma, tp, T)
    a3 = equity_path_A3(S0, C, K_eff, RF_RATE, sigma, tp, T)
    return a1 + a2 + a3


def add_ccb_pricing(df: pd.DataFrame) -> pd.DataFrame:
    out = df.copy()
    out["CCB股权价值"] = out.apply(ccb_equity_value, axis=1)
    out["CCB理论价值"] = out["CCB股权价值"] + out["纯债价值"]
    out["CCB定价偏离因子"] = out["债券收盘价"] / out["CCB理论价值"] - 1
    return out


def calculate_additional_factors(df: pd.DataFrame) -> pd.DataFrame:
    out = df.copy()
    price = out["债券收盘价"]
    conv = out["转换价值"].replace(0, np.nan)
    pure = out["纯债价值"].replace(0, np.nan)

    out["转股溢价率"] = (price - conv) / conv
    out["纯债溢价率"] = (price - pure) / pure
    out["转股价值占比"] = conv / price.replace(0, np.nan)
    out["纯债价值占比"] = pure / price.replace(0, np.nan)
    return out


def classify_convertible_bonds(df: pd.DataFrame) -> pd.DataFrame:
    out = df.copy()

    def _cls(row):
        cp = row["转股溢价率"]
        bp = row["纯债溢价率"]
        cr = row["转股价值占比"]
        if pd.isna(cp) or pd.isna(bp) or pd.isna(cr):
            return "未分类"
        if (cp < 0.20) and (cr > 0.70):
            return "偏股型"
        if (bp < 0.30) and (cr < 0.50):
            return "偏债型"
        return "平衡型"

    out["转债类型"] = out.apply(_cls, axis=1)
    return out


if __name__ == "__main__":
    df = efficient_data_merge()
    df = add_ccb_pricing(df)
    df = calculate_additional_factors(df)
    df = classify_convertible_bonds(df)

    print(f"最终数据形状: {df.shape}")
    print("样例：")
    print(df.head())

    out_dir = Path(os.path.expanduser("~/Desktop/转债量化"))
    out_dir.mkdir(parents=True, exist_ok=True)
    out_file = out_dir / "转债量化分析结果.xlsx"
    df.to_excel(out_file, index=False)
    print(f"已保存: {out_file}")