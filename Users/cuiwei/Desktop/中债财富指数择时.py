#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中债财富指数择时分析
"""

import os
import warnings
from datetime import datetime

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

from sklearn.preprocessing import RobustScaler
from sklearn.feature_selection import SelectKBest, f_classif
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.pipeline import Pipeline
from sklearn.metrics import roc_auc_score

warnings.filterwarnings("ignore")
plt.rcParams["font.family"] = "DejaVu Sans"
plt.rcParams["axes.unicode_minus"] = False


class BondTimingStable:
    def __init__(self, data_path, output_dir="/Users/<USER>/Desktop/领先指数结果_stable"):
        self.data_path = data_path
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        self.risk_free_annual = 0.01
        self.df_bond = None
        self.df_lead = None
        self.df_bench = None
        self.df = None
        self.pred = None
        self.results = None
        self.metrics = None
        self.feature_cols = None

    def load(self):
        self.df_bond = pd.read_excel(self.data_path, sheet_name=0, index_col=0)
        self.df_bond.index = pd.to_datetime(self.df_bond.index)
        self.df_bond.columns = [
            "Bond_Index","Bond_Change","Bond_Return_Pct","Settlement_Volume",
            "Avg_BPV","Avg_Mkt_Convex","Avg_Mkt_Dur","Avg_Mkt_YTM",
            "Avg_CF_Convex","Avg_CF_Dur","Avg_CF_YTM",
            "Avg_Dividend","Avg_RemTerm","Total_MV"
        ]

        self.df_lead = pd.read_excel(self.data_path, sheet_name=1)
        self.df_lead.columns = ["Date", "Leading_Index"]
        self.df_lead["Date"] = pd.to_datetime(self.df_lead["Date"])
        self.df_lead.set_index("Date", inplace=True)

        self.df_bench = pd.read_excel(self.data_path, sheet_name=2)
        self.df_bench.columns = ["Date", "Benchmark_Index"]
        self.df_bench["Date"] = pd.to_datetime(self.df_bench["Date"])
        self.df_bench.set_index("Date", inplace=True)
        return self

    def build_features(self):
        df = self.df_bond.copy()
        df["ret"] = df["Bond_Index"].pct_change()
        for k in [1, 3, 5, 10]:
            df[f"fwd_ret_{k}d"] = df["Bond_Index"].pct_change(k).shift(-k)
            df[f"target_{k}d"] = (df[f"fwd_ret_{k}d"] > 0).astype(int)

        start = max(df.index.min(), self.df_lead.index.min())
        end = min(df.index.max(), self.df_lead.index.max(), self.df_bench.index.max())
        days = pd.date_range(start, end, freq="B")
        lead_daily = self.df_lead.reindex(days).ffill()

        df = df.reindex(days).join(lead_daily, how="inner").join(self.df_bench.reindex(days).ffill(), how="inner")

        for lag in range(1, 31):
            df[f"lead_lag_{lag}"] = df["Leading_Index"].shift(lag)

        for w in [5, 10, 20, 30]:
            df[f"ma_{w}"] = df["Bond_Index"].rolling(w).mean()
            df[f"px_ma_{w}"] = df["Bond_Index"] / df[f"ma_{w}"]
            df[f"lead_ma_{w}"] = df["Leading_Index"].rolling(w).mean()
            df[f"lead_ma_ratio_{w}"] = df["Leading_Index"] / df[f"lead_ma_{w}"]

        for w in [5, 10, 20]:
            df[f"vol_{w}"] = df["ret"].rolling(w).std()
            df[f"lead_vol_{w}"] = df["Leading_Index"].pct_change().rolling(w).std()

        for w in [5, 10, 20]:
            df[f"mom_{w}"] = df["Bond_Index"].pct_change(w)
            df[f"lead_mom_{w}"] = df["Leading_Index"].pct_change(w)

        def rsi(s, n=14):
            d = s.diff()
            up = d.clip(lower=0).rolling(n).mean()
            dn = (-d.clip(upper=0)).rolling(n).mean()
            rs = up / (dn.replace(0, np.nan))
            return 100 - 100 / (1 + rs)

        df["rsi_14"] = rsi(df["Bond_Index"], 14)
        df["lead_rsi_14"] = rsi(df["Leading_Index"], 14)

        for w in [10, 20]:
            m = df["Bond_Index"].rolling(w).mean()
            sd = df["Bond_Index"].rolling(w).std()
            upper = m + 2 * sd
            lower = m - 2 * sd
            df[f"bb_pos_{w}"] = (df["Bond_Index"] - lower) / (upper - lower)

        df["bench_ret"] = df["Benchmark_Index"].pct_change()
        df = df.dropna().copy()
        self.df = df
        return self

    def _make_pipeline(self):
        pipe = Pipeline([
            ("scaler", RobustScaler()),
            ("sel", SelectKBest(f_classif, k=12)),
            ("clf", LogisticRegression(C=1.0, penalty="l2", max_iter=400, n_jobs=None))
        ])
        grid = {
            "sel__k": [8, 12, 16, 20],
            "clf__C": [0.2, 0.5, 1.0, 2.0]
        }
        return pipe, grid

    def walk_forward(self, target_horizon="target_5d",
                     init_train_days=500, refit_every=60, random_state=42):
        df = self.df.copy()
        y = df[target_horizon].astype(int)

        lead_feats = [c for c in df.columns if c.startswith("lead_lag_")]
        other_feats = [
            "px_ma_5","px_ma_10","px_ma_20",
            "lead_ma_ratio_5","lead_ma_ratio_10","lead_ma_ratio_20",
            "vol_5","vol_10","vol_20",
            "mom_5","mom_10","mom_20",
            "lead_mom_5","lead_mom_10","lead_mom_20",
            "rsi_14","lead_rsi_14",
            "bb_pos_10","bb_pos_20",
            "Avg_Mkt_Dur","Avg_Mkt_YTM","Settlement_Volume"
        ]
        feats = [c for c in lead_feats + other_feats if c in df.columns]
        self.feature_cols = feats

        proba = pd.Series(index=df.index, dtype=float)
        pipe, grid = self._make_pipeline()

        start_idx = init_train_days
        dates = df.index
        tscv = TimeSeriesSplit(n_splits=5)

        while start_idx < len(dates):
            train_end = dates[start_idx]
            test_end_idx = min(start_idx + refit_every, len(dates))
            test_slice = slice(train_end, dates[test_end_idx - 1])

            train_idx = df.index < train_end
            test_idx = (df.index >= train_end) & (df.index <= dates[test_end_idx - 1])

            if train_idx.sum() < 200 or test_idx.sum() == 0:
                start_idx += refit_every
                continue

            X_train = df.loc[train_idx, feats]
            y_train = y.loc[train_idx]
            X_test = df.loc[test_idx, feats]

            gs = GridSearchCV(pipe, grid, cv=tscv, scoring="roc_auc", n_jobs=-1, refit=True)
            gs.fit(X_train, y_train)
            p = gs.best_estimator_.predict_proba(X_test)[:, 1]
            proba.loc[test_idx] = p

            start_idx += refit_every

        self.pred = proba.dropna()
        return self

    def make_signals(self, buy_thr=0.55, sell_thr=0.45):
        df = self.df.copy()
        df = df.join(self.pred.rename("proba"), how="left")
        df["proba"] = df["proba"].ffill()  # 预测在两次重训间保持不变

        long_gate = (df["px_ma_5"] > 1.0) & (df["lead_ma_ratio_5"] > 1.0)
        short_gate = (df["px_ma_5"] < 1.0) & (df["lead_ma_ratio_5"] < 1.0)

        sig = np.zeros(len(df), dtype=int)
        for i in range(1, len(df)):
            prev = sig[i-1]
            pb = df["proba"].iloc[i]
            if (pb >= buy_thr) and long_gate.iloc[i]:
                sig[i] = 1
            elif (pb <= sell_thr) and short_gate.iloc[i]:
                sig[i] = 0
            else:
                sig[i] = prev

        df["signal"] = sig
        df["buy_signal"] = (df["signal"].diff() == 1)
        df["sell_signal"] = (df["signal"].diff() == -1)
        self.df = df
        return self

    def backtest(self, start="2010-01-01", end=None):
        df = self.df.copy()
        if end is None:
            end = df.index.max()
        df = df.loc[(df.index >= pd.to_datetime(start)) & (df.index <= pd.to_datetime(end))].copy()

        rf_daily = self.risk_free_annual / 252.0
        df["str_ret"] = np.where(df["signal"].shift(1) == 1, df["ret"], rf_daily)
        df["bench_ret"] = df["bench_ret"].fillna(0)

        df["str_nav"] = (1 + df["str_ret"]).cumprod()
        df["bench_nav"] = (1 + df["bench_ret"]).cumprod()
        df["excess_ret"] = df["str_ret"] - df["bench_ret"]
        df["excess_nav"] = (1 + df["excess_ret"]).cumprod()

        self.results = df
        return self

    @staticmethod
    def _mdd(nav: pd.Series):
        peak = nav.cummax()
        dd = nav / peak - 1
        mdd = float(dd.min())
        end = dd.idxmin()
        start = nav.loc[:end].idxmax()
        return mdd, start, end

    def evaluate(self):
        df = self.results.copy()
        n = len(df)
        yrs = n / 252.0
        str_ann = (df["str_nav"].iloc[-1]) ** (1 / yrs) - 1
        ben_ann = (df["bench_nav"].iloc[-1]) ** (1 / yrs) - 1
        str_vol = df["str_ret"].std(ddof=0) * np.sqrt(252)
        ben_vol = df["bench_ret"].std(ddof=0) * np.sqrt(252)
        sharpe = (str_ann - self.risk_free_annual) / str_vol if str_vol > 0 else 0.0
        mdd, s, e = self._mdd(df["str_nav"])
        calmar = str_ann / abs(mdd) if mdd < 0 else np.nan
        win = (df.loc[df["signal"].shift(1) == 1, "str_ret"] > df.loc[df["signal"].shift(1) == 1, "bench_ret"]).mean()
        ir = (df["excess_ret"].mean() / df["excess_ret"].std(ddof=0) * np.sqrt(252)) if df["excess_ret"].std(ddof=0) > 0 else 0.0

        self.metrics = pd.Series({
            "样本天数": n,
            "年化收益": str_ann,
            "基准年化": ben_ann,
            "年化波动": str_vol,
            "基准波动": ben_vol,
            "夏普": sharpe,
            "最大回撤": mdd,
            "回撤开始": s, "回撤结束": e,
            "卡玛比率": calmar,
            "主动持仓胜率": win,
            "信息比率": ir,
            "最终净值": df["str_nav"].iloc[-1],
            "基准净值": df["bench_nav"].iloc[-1]
        })
        return self

    def plot(self):
        df = self.results.copy()

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 10))
        ax1.plot(df.index, df["str_nav"], label="Strategy NAV", lw=2)
        ax1.plot(df.index, df["bench_nav"], label="Benchmark NAV", lw=2)
        b = df[df["buy_signal"]]; s = df[df["sell_signal"]]
        if not b.empty:
            ax1.scatter(b.index, df.loc[b.index, "str_nav"], marker="^", c="g", s=60, label="Buy")
        if not s.empty:
            ax1.scatter(s.index, df.loc[s.index, "str_nav"], marker="v", c="r", s=60, label="Sell")
        ax1.legend(); ax1.grid(alpha=0.3); ax1.set_title("NAV")

        peak = df["str_nav"].cummax()
        dd = df["str_nav"] / peak - 1
        ax2.fill_between(df.index, dd, 0, color="gray", alpha=0.3, step="pre")
        ax2.set_title("Drawdown"); ax2.grid(alpha=0.3)

        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "nav_drawdown.png"), dpi=200)
        plt.close()

        fig, ax = plt.subplots(figsize=(16, 5))
        ax.plot(df.index, df["excess_nav"], lw=2, c="tab:green")
        ax.axhline(1.0, ls="--", c="k", alpha=0.4)
        ax.set_title("Excess Return NAV"); ax.grid(alpha=0.3)
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, "excess_nav.png"), dpi=200)
        plt.close()
        return self

    def export(self):
        xlsx = os.path.join(self.output_dir, "Bond_Timing_Stable.xlsx")
        with pd.ExcelWriter(xlsx, engine="openpyxl") as w:
            self.results[["str_nav","bench_nav","excess_nav","signal"]].to_excel(w, sheet_name="NAV_Signal")
            self.metrics.to_frame("Value").to_excel(w, sheet_name="Metrics")
            if self.pred is not None:
                self.pred.rename("proba").to_frame().to_excel(w, sheet_name="OOS_Proba")
        return self


if __name__ == "__main__":
    analyzer = BondTimingStable("/Users/<USER>/Desktop/中债财富指数择时.xlsx")
    (analyzer
     .load()
     .build_features()
     .walk_forward(target_horizon="target_5d", init_train_days=500, refit_every=60)
     .make_signals(buy_thr=0.55, sell_thr=0.45)
     .backtest(start="2012-01-01")
     .evaluate()
     .plot()
     .export()
    )

    print(analyzer.metrics.round(4))