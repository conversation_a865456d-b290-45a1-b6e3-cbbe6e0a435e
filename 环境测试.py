#!/usr/bin/env python3
"""
环境和数据验证脚本
在运行主要分析之前，使用此脚本检查环境配置
"""

import sys
import os

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major >= 3 and version.minor >= 7:
        print("✓ Python版本符合要求")
        return True
    else:
        print("✗ Python版本过低，建议使用Python 3.7+")
        return False

def check_required_packages():
    """检查必需的包"""
    print("\n检查必需的包...")
    
    required_packages = [
        'pandas', 'numpy', 'matplotlib', 'seaborn', 
        'scipy', 'sklearn', 'statsmodels', 'plotly'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'sklearn':
                import sklearn
                print(f"✓ scikit-learn: {sklearn.__version__}")
            else:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"✓ {package}: {version}")
        except ImportError:
            print(f"✗ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺失的包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    else:
        print("✓ 所有必需的包都已安装")
        return True

def check_data_file():
    """检查数据文件"""
    print("\n检查数据文件...")
    
    data_path = '/Users/<USER>/Desktop/TEXT_Trader_Quant_dataset.csv'
    
    if os.path.exists(data_path):
        print(f"✓ 数据文件存在: {data_path}")
        
        # 检查文件大小
        file_size = os.path.getsize(data_path) / (1024 * 1024)  # MB
        print(f"  文件大小: {file_size:.2f} MB")
        
        # 尝试读取文件头部
        try:
            import pandas as pd
            df = pd.read_csv(data_path, nrows=5)
            print(f"  数据形状（前5行）: {df.shape}")
            print(f"  列名: {list(df.columns)}")
            
            # 检查第10列
            if '10' in df.columns:
                print(f"  第10列存在，数据类型: {df['10'].dtype}")
                unique_values = df['10'].unique()
                print(f"  第10列唯一值: {sorted(unique_values)}")
            else:
                print("  ⚠ 警告: 未找到第10列")
            
            return True
            
        except Exception as e:
            print(f"✗ 读取数据文件时出错: {e}")
            return False
    else:
        print(f"✗ 数据文件不存在: {data_path}")
        print("  请确认文件路径是否正确")
        return False

def check_jupyter():
    """检查Jupyter环境"""
    print("\n检查Jupyter环境...")
    
    try:
        import jupyter
        print("✓ Jupyter已安装")
        
        # 检查nbconvert
        try:
            import nbconvert
            print(f"✓ nbconvert已安装: {nbconvert.__version__}")
            return True
        except ImportError:
            print("✗ nbconvert未安装")
            print("  请运行: pip install nbconvert")
            return False
            
    except ImportError:
        print("✗ Jupyter未安装")
        print("  请运行: pip install jupyter")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        
        # 创建测试数据
        test_data = pd.DataFrame({
            'A': np.random.randn(100),
            'B': np.random.randn(100),
            'C': np.random.choice([0, 1, 2], 100)
        })
        
        # 测试基本操作
        corr = test_data[['A', 'B']].corr()
        print(f"✓ 相关性计算正常")
        
        # 测试绘图（不显示）
        plt.ioff()  # 关闭交互模式
        fig, ax = plt.subplots()
        ax.plot(test_data['A'])
        plt.close(fig)
        print(f"✓ 绘图功能正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 基本功能测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("高维时间序列数据分析 - 环境验证")
    print("=" * 60)
    
    checks = [
        check_python_version(),
        check_required_packages(),
        check_data_file(),
        check_jupyter(),
        test_basic_functionality()
    ]
    
    print("\n" + "=" * 60)
    print("验证结果汇总:")
    print("=" * 60)
    
    if all(checks):
        print("✓ 所有检查通过！环境配置正确，可以开始分析。")
        print("\n下一步:")
        print("1. 运行 jupyter notebook")
        print("2. 打开 '高维时间序列数据分析.ipynb'")
        print("3. 依次执行所有代码单元格")
        print("4. 运行 python convert_notebook.py 生成HTML文件")
    else:
        print("✗ 部分检查未通过，请根据上述提示解决问题。")
        print("\n常见解决方案:")
        print("1. 安装缺失的包: pip install -r requirements.txt")
        print("2. 检查数据文件路径")
        print("3. 升级Python版本（如需要）")

if __name__ == "__main__":
    main()
