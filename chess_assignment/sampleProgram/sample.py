from abc import ABC, abstractmethod
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from chessLib.move import <PERSON><PERSON><PERSON>
from chessLib.position import Position
import random


class BaseGame(ABC):
    @abstractmethod
    def play(self, moves: int):
        pass

    @abstractmethod
    def setup(self):
        pass


class SimpleGame(BaseGame):
    def __init__(self):
        self.__startPosition = None

    def play(self, moves: int):
        knight = KnightMove()
        pos = self.__startPosition
        print("0: My position is " + pos.to_string())

        for i in range(moves):
            possible_moves = knight.valid_moves(pos)
            r = random.randrange(0, possible_moves.__len__())
            pos = possible_moves[r]
            print(str(i + 1) + ": My position is " + pos.to_string())

    def setup(self):
        self.__startPosition = Position(3, 3)
