from sample import SimpleGame
from answer import ComplexGame

if __name__ == '__main__':
    print("Chess Game Demonstration")
    print("=" * 50)
    
    # Demonstrate SimpleGame (original implementation)
    print("\n1. Running SimpleGame (Single Knight):")
    print("-" * 40)
    simple_game = SimpleGame()
    simple_game.setup()
    simple_game.play(5)
    
    print("\n" + "=" * 50)
    
    # Demonstrate ComplexGame (extended implementation)
    print("\n2. Running ComplexGame (Multiple Pieces):")
    print("-" * 40)
    complex_game = ComplexGame()
    complex_game.setup()
    complex_game.play(15)
    
    # Display final statistics
    print("\n" + "=" * 50)
    print("Game Statistics:")
    stats = complex_game.get_game_statistics()
    print(f"Total pieces on board: {stats['total_pieces']}")
    print(f"Piece distribution: {stats['piece_counts']}")
    print(f"Total moves executed: {stats['moves_executed']}")
    print("=" * 50)
