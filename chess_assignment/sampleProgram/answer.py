import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from sample import BaseGame
from chessLib.position import Position
from pieces import <PERSON>, <PERSON>, Queen, Piece
from board import GameBoard
import random
from typing import List


class ComplexGame(BaseGame):
    """
    Extended game implementation supporting multiple piece types:
    <PERSON>, <PERSON>, and <PERSON> on an 8x8 board.
    
    Game Rules:
    - Only one piece can occupy any position at a given time
    - All pieces can "jump" over occupied positions
    - Each move randomly selects a piece and moves it to a random valid position
    """
    
    def __init__(self):
        self.board = GameBoard()
        self.move_count = 0
    
    def setup(self):
        """
        Initialize the game board with predefined pieces in strategic positions.
        Places pieces to demonstrate different movement patterns and interactions.
        """
        # Define initial piece positions
        initial_pieces = [
            Knight(Position(2, 1)),   # <PERSON> in bottom left area
            Knight(Position(7, 8)),   # <PERSON> in top right area
            <PERSON>(Position(1, 1)),   # <PERSON> in corner
            <PERSON>(Position(8, 8)),   # <PERSON> in opposite corner
            <PERSON>(Position(4, 4)),    # <PERSON> in center
            Queen(Position(5, 5)),    # Second queen near center
        ]
        
        # Add pieces to the board
        for piece in initial_pieces:
            success = self.board.add_piece(piece)
            if not success:
                print(f"Warning: Could not place {piece} - position may be occupied")
        
        print("=== Game Setup Complete ===")
        print(self.board.get_board_status())
        print("\nInitial Board Layout:")
        print(self.board.display_board())
        print("Legend: K=Knight, B=Bishop, Q=Queen, .=Empty")
        print("=" * 50)
    
    def play(self, moves: int):
        """
        Execute the specified number of game moves.
        Each move:
        1. Randomly selects a piece from all pieces on the board
        2. Gets all valid moves for that piece
        3. Randomly selects one valid move
        4. Executes the move and displays the result
        
        Args:
            moves: Number of moves to execute
        """
        print(f"\n=== Starting Game: {moves} moves ===\n")
        
        for move_num in range(1, moves + 1):
            self.move_count = move_num
            
            # Get all pieces currently on the board
            all_pieces = self.board.get_all_pieces()
            
            if not all_pieces:
                print("No pieces left on board! Game ended.")
                break
            
            # Randomly select a piece
            selected_piece = random.choice(all_pieces)
            
            # Get valid moves for the selected piece
            valid_moves = self.board.get_valid_moves_for_piece(selected_piece)
            
            if not valid_moves:
                print(f"Move {move_num}: {selected_piece} has no valid moves! Skipping turn.")
                continue
            
            # Randomly select a valid move
            target_position = random.choice(valid_moves)
            
            # Record the original position
            original_position = Position(selected_piece.position.x, selected_piece.position.y)
            
            # Execute the move
            move_success = self.board.move_piece(selected_piece.position, target_position)
            
            if move_success:
                print(f"Move {move_num}: {selected_piece.piece_type} moved from "
                      f"{original_position.to_string()} to {target_position.to_string()}")
                
                # Display board every 5 moves or on last move
                if move_num % 5 == 0 or move_num == moves:
                    print(f"\nBoard after move {move_num}:")
                    print(self.board.display_board())
            else:
                print(f"Move {move_num}: Failed to move {selected_piece} to {target_position.to_string()}")
        
        print(f"\n=== Game Complete: {self.move_count} moves executed ===")
        print("Final board state:")
        print(self.board.display_board())
        print(self.board.get_board_status())
    
    def get_game_statistics(self) -> dict:
        """
        Get current game statistics
        Returns:
            Dictionary containing game statistics
        """
        pieces = self.board.get_all_pieces()
        piece_counts = {}
        
        for piece in pieces:
            piece_type = piece.piece_type
            piece_counts[piece_type] = piece_counts.get(piece_type, 0) + 1
        
        return {
            'total_pieces': len(pieces),
            'piece_counts': piece_counts,
            'moves_executed': self.move_count,
            'occupied_positions': len(self.board.get_occupied_positions())
        }
    
    def add_custom_piece(self, piece: Piece) -> bool:
        """
        Add a custom piece to the board (for testing/extension)
        Args:
            piece: Piece to add
        Returns:
            True if successful, False otherwise
        """
        return self.board.add_piece(piece)
