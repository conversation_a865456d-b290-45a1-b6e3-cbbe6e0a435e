from abc import ABC, abstractmethod
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from chessLib.position import Position
from chessLib.move import KnightMove


class Piece(ABC):
    """Abstract base class for all chess pieces"""
    
    def __init__(self, position: Position, piece_type: str):
        self.position = position
        self.piece_type = piece_type
    
    @abstractmethod
    def get_valid_moves(self, occupied_positions: set) -> list:
        """
        Get all valid moves for this piece, considering occupied positions
        Args:
            occupied_positions: Set of Position objects that are currently occupied
        Returns:
            List of Position objects representing valid moves
        """
        pass
    
    def is_position_valid(self, pos: Position) -> bool:
        """Check if position is within board boundaries"""
        return 1 <= pos.x <= 8 and 1 <= pos.y <= 8
    
    def __str__(self):
        return f"{self.piece_type} at {self.position.to_string()}"


class Knight(Piece):
    """Knight piece implementation using ChessLib"""
    
    def __init__(self, position: Position):
        super().__init__(position, "Knight")
        self.__knight_move = KnightMove()
    
    def get_valid_moves(self, occupied_positions: set) -> list:
        """Get valid knight moves, excluding occupied positions"""
        all_moves = self.__knight_move.valid_moves(self.position)
        # Filter out occupied positions (pieces can jump over others but not land on them)
        valid_moves = [move for move in all_moves if move not in occupied_positions]
        return valid_moves


class Bishop(Piece):
    """Bishop piece implementation - moves diagonally any distance"""
    
    def __init__(self, position: Position):
        super().__init__(position, "Bishop")
    
    def get_valid_moves(self, occupied_positions: set) -> list:
        """Get valid bishop moves - diagonal in all 4 directions"""
        valid_moves = []
        
        # Four diagonal directions: NE, NW, SE, SW
        directions = [(1, 1), (1, -1), (-1, 1), (-1, -1)]
        
        for dx, dy in directions:
            # Move in each direction until hitting board boundary
            for distance in range(1, 8):  # Maximum 7 squares in any direction
                new_x = self.position.x + (dx * distance)
                new_y = self.position.y + (dy * distance)
                new_pos = Position(new_x, new_y)
                
                # Check if position is within board boundaries
                if not self.is_position_valid(new_pos):
                    break
                
                # Add position if not occupied (pieces can jump over others)
                if new_pos not in occupied_positions:
                    valid_moves.append(new_pos)
        
        return valid_moves


class Queen(Piece):
    """Queen piece implementation - moves diagonally, horizontally, or vertically any distance"""
    
    def __init__(self, position: Position):
        super().__init__(position, "Queen")
    
    def get_valid_moves(self, occupied_positions: set) -> list:
        """Get valid queen moves - diagonal, horizontal, and vertical"""
        valid_moves = []
        
        # Eight directions: N, S, E, W, NE, NW, SE, SW
        directions = [
            (0, 1),   # North
            (0, -1),  # South
            (1, 0),   # East
            (-1, 0),  # West
            (1, 1),   # Northeast
            (1, -1),  # Southeast
            (-1, 1),  # Northwest
            (-1, -1)  # Southwest
        ]
        
        for dx, dy in directions:
            # Move in each direction until hitting board boundary
            for distance in range(1, 8):  # Maximum 7 squares in any direction
                new_x = self.position.x + (dx * distance)
                new_y = self.position.y + (dy * distance)
                new_pos = Position(new_x, new_y)
                
                # Check if position is within board boundaries
                if not self.is_position_valid(new_pos):
                    break
                
                # Add position if not occupied (pieces can jump over others)
                if new_pos not in occupied_positions:
                    valid_moves.append(new_pos)
        
        return valid_moves
