import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from chessLib.position import Position
from pieces import Piece
from typing import Dict, Set, List


class GameBoard:
    """Manages the 8x8 game board and piece positions"""
    
    def __init__(self):
        self.pieces: Dict[Position, Piece] = {}  # Maps positions to pieces
        self.board_size = 8
    
    def add_piece(self, piece: Piece) -> bool:
        """
        Add a piece to the board
        Args:
            piece: Piece object to add
        Returns:
            True if piece was added successfully, False if position is occupied
        """
        if piece.position in self.pieces:
            return False  # Position already occupied
        
        if not self._is_valid_position(piece.position):
            return False  # Position outside board boundaries
        
        self.pieces[piece.position] = piece
        return True
    
    def remove_piece(self, position: Position) -> Piece:
        """
        Remove a piece from the board
        Args:
            position: Position to remove piece from
        Returns:
            The removed piece, or None if no piece at position
        """
        return self.pieces.pop(position, None)
    
    def move_piece(self, from_pos: Position, to_pos: Position) -> bool:
        """
        Move a piece from one position to another
        Args:
            from_pos: Current position of piece
            to_pos: Target position
        Returns:
            True if move was successful, False otherwise
        """
        if from_pos not in self.pieces:
            return False  # No piece at source position
        
        if to_pos in self.pieces:
            return False  # Target position is occupied
        
        if not self._is_valid_position(to_pos):
            return False  # Target position outside board
        
        # Move the piece
        piece = self.pieces.pop(from_pos)
        piece.position = to_pos
        self.pieces[to_pos] = piece
        return True
    
    def get_piece_at(self, position: Position) -> Piece:
        """Get the piece at a specific position"""
        return self.pieces.get(position)
    
    def get_all_pieces(self) -> List[Piece]:
        """Get all pieces currently on the board"""
        return list(self.pieces.values())
    
    def get_occupied_positions(self) -> Set[Position]:
        """Get all currently occupied positions"""
        return set(self.pieces.keys())
    
    def get_valid_moves_for_piece(self, piece: Piece) -> List[Position]:
        """
        Get all valid moves for a specific piece
        Args:
            piece: The piece to get moves for
        Returns:
            List of valid Position objects
        """
        occupied_positions = self.get_occupied_positions()
        # Remove the piece's current position from occupied positions
        # since it can move away from its current position
        occupied_positions.discard(piece.position)
        
        return piece.get_valid_moves(occupied_positions)
    
    def _is_valid_position(self, position: Position) -> bool:
        """Check if position is within board boundaries"""
        return 1 <= position.x <= self.board_size and 1 <= position.y <= self.board_size
    
    def display_board(self) -> str:
        """
        Create a visual representation of the board
        Returns:
            String representation of the board
        """
        board_str = "  1 2 3 4 5 6 7 8\n"
        
        for y in range(8, 0, -1):  # Start from top (8) to bottom (1)
            row_str = f"{y} "
            for x in range(1, 9):
                pos = Position(x, y)
                piece = self.get_piece_at(pos)
                if piece:
                    # Use first letter of piece type
                    symbol = piece.piece_type[0]
                else:
                    symbol = "."
                row_str += symbol + " "
            board_str += row_str + "\n"
        
        return board_str
    
    def get_board_status(self) -> str:
        """Get a summary of all pieces on the board"""
        if not self.pieces:
            return "Board is empty"
        
        status = "Current pieces on board:\n"
        for piece in self.get_all_pieces():
            status += f"  {piece}\n"
        
        return status
