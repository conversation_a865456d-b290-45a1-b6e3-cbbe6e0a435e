# Chess Game Assignment Solution

## Overview
This project extends a simple knight-based chess game to support multiple piece types (<PERSON>, <PERSON>, Queen) on an 8×8 board using Object-Oriented Design principles.

## Project Structure
```
chess_assignment/
├── chessLib/                 # Third-party library (unchanged)
│   ├── __init__.py
│   ├── position.py          # Position class for board coordinates
│   └── move.py              # KnightMove class for knight movement logic
├── sampleProgram/           # Main game implementation
│   ├── sample.py            # Original SimpleGame and BaseGame classes
│   ├── pieces.py            # Piece classes (<PERSON>, <PERSON>, <PERSON>)
│   ├── board.py             # GameBoard class for board management
│   ├── answer.py            # ComplexGame implementation (main solution)
│   └── program.py           # Main program entry point
├── programTests/            # Unit tests
│   ├── __init__.py
│   ├── movetest.py          # Original ChessLib tests
│   └── answertest.py        # Tests for ComplexGame implementation
└── README.md                # This file
```

## Key Features

### 1. Object-Oriented Design
- **Abstract Base Classes**: `Piece` class provides common interface for all pieces
- **Inheritance**: `<PERSON>`, `<PERSON>`, `Queen` inherit from `Piece`
- **Polymorphism**: All pieces implement `get_valid_moves()` method
- **Encapsulation**: Board state managed through `GameBoard` class

### 2. Game Rules Implementation
- **Single Occupancy**: Only one piece per board position
- **Jump Capability**: All pieces can "jump" over occupied positions
- **Boundary Checking**: All moves respect 8×8 board boundaries
- **Random Movement**: Each turn randomly selects a piece and valid move

### 3. Piece Movement Patterns
- **Knight**: L-shaped moves (using ChessLib implementation)
- **Bishop**: Diagonal movement, any distance within boundaries
- **Queen**: Horizontal, vertical, and diagonal movement, any distance

### 4. Board Management
- **Position Tracking**: Maintains current positions of all pieces
- **Move Validation**: Ensures moves are legal and positions are available
- **Visual Display**: ASCII representation of current board state
- **Statistics**: Tracks game progress and piece distribution

## Usage

### Running the Game
```bash
cd chess_assignment/sampleProgram
python program.py
```

### Running Tests
```bash
cd chess_assignment/programTests
python movetest.py      # Test ChessLib functionality
python answertest.py    # Test ComplexGame implementation
```

## Implementation Details

### ComplexGame Class
The main solution is implemented in `answer.py` as the `ComplexGame` class:

- **setup()**: Initializes board with 6 pieces (2 Knights, 2 Bishops, 2 Queens)
- **play(moves)**: Executes specified number of random moves
- **Game Statistics**: Tracks and reports game state

### Design Patterns Used
1. **Template Method**: `BaseGame` defines game structure
2. **Strategy Pattern**: Different movement strategies for each piece type
3. **Factory Pattern**: Piece creation and board setup
4. **Observer Pattern**: Board state monitoring and display

### Key Design Decisions
1. **Extensibility**: Easy to add new piece types by extending `Piece` class
2. **Separation of Concerns**: Board logic separate from piece logic
3. **Error Handling**: Graceful handling of invalid moves and edge cases
4. **Testing**: Comprehensive unit tests for all major functionality

## Game Flow
1. **Setup Phase**: Place pieces on predefined board positions
2. **Game Loop**: For each move:
   - Randomly select a piece from all pieces on board
   - Get all valid moves for selected piece
   - Randomly select one valid move
   - Execute move and update board state
   - Display progress and board state
3. **Completion**: Display final statistics and board state

## Extension Points
The design supports easy extension for:
- New piece types (implement `Piece` interface)
- Different board sizes (modify `GameBoard` class)
- Custom game rules (extend `ComplexGame` class)
- Advanced AI strategies (replace random selection logic)

## Testing Coverage
- ChessLib functionality verification
- Individual piece movement patterns
- Board boundary enforcement
- Collision detection and avoidance
- Game statistics and state management
- Error handling and edge cases
