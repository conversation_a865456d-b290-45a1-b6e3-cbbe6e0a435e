import unittest
import sys
import os

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'sampleProgram'))

from answer import ComplexGame
from pieces import <PERSON>, <PERSON>, Queen
from board import GameBoard
from chessLib.position import Position


class ComplexGameTests(unittest.TestCase):
    """Test cases for the ComplexGame implementation"""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        self.game = ComplexGame()
    
    def test_game_setup(self):
        """Test that game setup creates pieces correctly"""
        self.game.setup()
        pieces = self.game.board.get_all_pieces()
        
        # Should have 6 pieces total (2 knights, 2 bishops, 2 queens)
        self.assertEqual(len(pieces), 6)
        
        # Count piece types
        piece_types = [piece.piece_type for piece in pieces]
        self.assertEqual(piece_types.count('Knight'), 2)
        self.assertEqual(piece_types.count('Bishop'), 2)
        self.assertEqual(piece_types.count('Queen'), 2)
    
    def test_knight_moves(self):
        """Test knight movement patterns"""
        knight = Knight(Position(4, 4))
        board = GameBoard()
        board.add_piece(knight)
        
        valid_moves = board.get_valid_moves_for_piece(knight)
        
        # Knight at (4,4) should have 8 possible moves
        self.assertEqual(len(valid_moves), 8)
        
        # Verify L-shaped moves
        expected_positions = [
            Position(5, 6), Position(5, 2), Position(3, 6), Position(3, 2),
            Position(6, 5), Position(2, 5), Position(6, 3), Position(2, 3)
        ]
        
        for expected_pos in expected_positions:
            self.assertIn(expected_pos, valid_moves)
    
    def test_bishop_moves(self):
        """Test bishop movement patterns"""
        bishop = Bishop(Position(4, 4))
        board = GameBoard()
        board.add_piece(bishop)
        
        valid_moves = board.get_valid_moves_for_piece(bishop)
        
        # Bishop should have multiple diagonal moves
        self.assertGreater(len(valid_moves), 10)
        
        # Test some specific diagonal positions
        diagonal_positions = [
            Position(1, 1), Position(2, 2), Position(3, 3),  # SW diagonal
            Position(5, 5), Position(6, 6), Position(7, 7), Position(8, 8),  # NE diagonal
            Position(1, 7), Position(2, 6), Position(3, 5),  # NW diagonal
            Position(5, 3), Position(6, 2), Position(7, 1)   # SE diagonal
        ]
        
        for pos in diagonal_positions:
            self.assertIn(pos, valid_moves)
    
    def test_queen_moves(self):
        """Test queen movement patterns"""
        queen = Queen(Position(4, 4))
        board = GameBoard()
        board.add_piece(queen)
        
        valid_moves = board.get_valid_moves_for_piece(queen)
        
        # Queen should have many moves (horizontal, vertical, diagonal)
        self.assertGreater(len(valid_moves), 20)
        
        # Test horizontal moves
        horizontal_positions = [Position(1, 4), Position(2, 4), Position(3, 4),
                               Position(5, 4), Position(6, 4), Position(7, 4), Position(8, 4)]
        for pos in horizontal_positions:
            self.assertIn(pos, valid_moves)
        
        # Test vertical moves
        vertical_positions = [Position(4, 1), Position(4, 2), Position(4, 3),
                             Position(4, 5), Position(4, 6), Position(4, 7), Position(4, 8)]
        for pos in vertical_positions:
            self.assertIn(pos, valid_moves)
    
    def test_piece_collision_avoidance(self):
        """Test that pieces cannot move to occupied positions"""
        board = GameBoard()
        
        # Place two pieces
        knight = Knight(Position(2, 2))
        bishop = Bishop(Position(4, 4))
        
        board.add_piece(knight)
        board.add_piece(bishop)
        
        # Get valid moves for bishop
        bishop_moves = board.get_valid_moves_for_piece(bishop)
        
        # Bishop should not be able to move to knight's position
        self.assertNotIn(Position(2, 2), bishop_moves)
    
    def test_board_boundaries(self):
        """Test that pieces respect board boundaries"""
        # Test knight at corner
        knight = Knight(Position(1, 1))
        board = GameBoard()
        board.add_piece(knight)
        
        valid_moves = board.get_valid_moves_for_piece(knight)
        
        # All moves should be within board boundaries
        for move in valid_moves:
            self.assertTrue(1 <= move.x <= 8)
            self.assertTrue(1 <= move.y <= 8)
    
    def test_game_statistics(self):
        """Test game statistics functionality"""
        self.game.setup()
        stats = self.game.get_game_statistics()
        
        self.assertEqual(stats['total_pieces'], 6)
        self.assertEqual(stats['moves_executed'], 0)  # No moves executed yet
        self.assertIn('Knight', stats['piece_counts'])
        self.assertIn('Bishop', stats['piece_counts'])
        self.assertIn('Queen', stats['piece_counts'])


if __name__ == '__main__':
    unittest.main()
