# -*- coding: utf-8 -*-
"""
利率综合领先指数构建模型 - 完整版
根据9大分类-2大分项-1个综合指数的层次化框架构建
"""

import pandas as pd
import numpy as np
from sklearn.decomposition import PCA
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 文件路径配置
FILE_PATH = '/Users/<USER>/Desktop/周频指数合成0804.xlsx'
OUTPUT_FILE_PATH = '/Users/<USER>/Desktop/利率领先指数_完整分层结果.xlsx'
TARGET_VARIABLE = '10Y国债收益率'

# 定义9大指标分类
CATEGORIES = {
    '工业上游生产': ['中国:高炉开工率(247家)', '石油沥青装置开工率', '中国:开工率:螺纹钢:主要钢厂'],
    '生产生活资料价格': ['南华工业品指数', 'CRB现货指数:综合', '秦皇岛港:平仓价:动力末煤', '中国:平均批发价:猪肉', '建材综合指数'],
    '地产与建筑': ['中国:100大中城市:成交土地溢价率', '中国:30大中城市:成交面积:商品房', '二手房挂牌指数', '中国:100大中城市:成交土地占地面积'],
    '耐用消费品': ['中国:日均销量(当周,厂家零售):乘用车', '主要家电零售额', '中国:日均销量(当周,厂家批发):乘用车'],
    '就业预期': ['互联网搜索指数:失业金领取条件'],
    '交通运输': ['地铁客运量'],
    '出口贸易': ['中国出口集装箱运价指数:综合指数'],
    '流动性与政策预期': ['利率互换:FR007:1年', '利率互换:FR007:5年',  '逆回购利率:7天'],
    '风险偏好与资产比价': ['铜金比', '1/沪深300PE（TTM）']
}

# 处理规则
PROCESSING_RULES = {
    # 生产类 - 季节性调整 + 水平值
    '中国:高炉开工率(247家)': {'seasonal': True, 'transform': 'level'},
    '石油沥青装置开工率': {'seasonal': True, 'transform': 'level'},
    '中国:开工率:螺纹钢:主要钢厂': {'seasonal': True, 'transform': 'level'},
    # 价格类 - 同比变化
    '南华工业品指数': {'seasonal': False, 'transform': 'yoy_52'},
    'CRB现货指数:综合': {'seasonal': False, 'transform': 'yoy_52'},
    '秦皇岛港:平仓价:动力末煤': {'seasonal': False, 'transform': 'yoy_52'},
    '中国:平均批发价:猪肉': {'seasonal': False, 'transform': 'yoy_52'},
    '建材综合指数': {'seasonal': False, 'transform': 'yoy_52'},
    # 地产类 - 混合处理
    '中国:100大中城市:成交土地溢价率': {'seasonal': False, 'transform': 'ma_4'},
    '中国:30大中城市:成交面积:商品房': {'seasonal': True, 'transform': 'yoy_52'},
    '二手房挂牌指数': {'seasonal': False, 'transform': 'pct_change_13'},
    '中国:100大中城市:成交土地占地面积': {'seasonal': True, 'transform': 'yoy_52'},
    # 消费类 - 季节性调整 + 同比
    '中国:日均销量(当周,厂家零售):乘用车': {'seasonal': True, 'transform': 'yoy_52'},
    '主要家电零售额': {'seasonal': True, 'transform': 'yoy_52'},
    '中国:日均销量(当周,厂家批发):乘用车': {'seasonal': True, 'transform': 'yoy_52'},
    # 其他基本面
    '互联网搜索指数:失业金领取条件': {'seasonal': False, 'transform': 'level'},
    '地铁客运量': {'seasonal': True, 'transform': 'level'},
    '中国出口集装箱运价指数:综合指数': {'seasonal': False, 'transform': 'yoy_52'},
    # 金融市场类 - 水平值
    '利率互换:FR007:1年': {'seasonal': False, 'transform': 'level'},
    '利率互换:FR007:5年': {'seasonal': False, 'transform': 'level'},
    '逆回购利率:7天': {'seasonal': False, 'transform': 'level'},
    '铜金比': {'seasonal': False, 'transform': 'level', 'direction': -1},  
    '1/沪深300PE（TTM）': {'seasonal': False, 'transform': 'level'},

}

def load_data():
    """加载和预处理数据"""
    print("正在加载数据...")
    df = pd.read_excel(FILE_PATH, sheet_name='总表', header=0)
    df = df.rename(columns={df.columns[0]: 'Date'})
    df['Date'] = pd.to_datetime(df['Date'])
    df = df.set_index('Date')
    
    # 处理重复索引
    if df.index.duplicated().any():
        print("发现重复日期，保留最后一个值")
        df = df[~df.index.duplicated(keep='last')]
    
    # 排序并填充缺失值
    df = df.sort_index()
    df = df.ffill().bfill()
    
    print(f"数据加载成功！形状: {df.shape}")
    print(f"时间范围: {df.index.min()} 到 {df.index.max()}")
    return df

def process_indicator(series, rule):
    """处理单个指标"""
    original_name = series.name
    
    # 1. 季节性调整（简化为52周差分）
    if rule.get('seasonal', False):
        series = series - series.shift(52)
    
    # 2. 数据变换
    transform = rule.get('transform', 'level')
    if transform == 'yoy_52':
        series = series.pct_change(periods=52) * 100
    elif transform == 'pct_change_13':
        series = series.pct_change(periods=13) * 100
    elif transform == 'ma_4':
        series = series.rolling(window=4).mean()
    
    # 3. 方向调整
    direction = rule.get('direction', 1)
    series = series * direction
    
    # 4. 清理数据
    series = series.replace([np.inf, -np.inf], np.nan).dropna()
    
    if len(series) < 20:  # 需要足够的数据点
        print(f"  警告: {original_name} 处理后数据点不足")
        return pd.Series(dtype=float, name=original_name)
    
    # 5. 标准化
    try:
        scaler = StandardScaler()
        processed = pd.Series(
            scaler.fit_transform(series.values.reshape(-1, 1)).flatten(),
            index=series.index,
            name=original_name
        )
        return processed
    except Exception as e:
        print(f"  警告: {original_name} 标准化失败: {e}")
        return pd.Series(dtype=float, name=original_name)

def extract_pca_component(data_df, category_name):
    """提取主成分"""
    if data_df.empty or len(data_df.columns) == 0:
        return pd.Series(dtype=float, name=category_name)
    
    # 删除全为NaN的行
    valid_data = data_df.dropna()
    
    if len(valid_data) < 20:
        print(f"  警告: {category_name} 有效数据点不足")
        return pd.Series(dtype=float, name=category_name)
    
    if len(data_df.columns) == 1:
        # 只有一个指标，直接返回
        return data_df.iloc[:, 0].rename(category_name)
    
    # 多个指标，使用PCA
    try:
        pca = PCA(n_components=1)
        pca_result = pca.fit_transform(valid_data)
        
        # 确保主成分方向与大部分成分股同向
        correlations = []
        for col in valid_data.columns:
            corr = np.corrcoef(pca_result.flatten(), valid_data[col])[0, 1]
            if not np.isnan(corr):
                correlations.append(corr)
        
        if correlations and np.mean(correlations) < 0:
            pca_result = -pca_result
        
        result = pd.Series(pca_result.flatten(), index=valid_data.index, name=category_name)
        
        # 解释方差比例
        explained_var = pca.explained_variance_ratio_[0]
        print(f"  {category_name} PCA解释方差比例: {explained_var:.3f}")
        
        return result.reindex(data_df.index)
        
    except Exception as e:
        print(f"  警告: {category_name} PCA失败: {e}")
        return pd.Series(dtype=float, name=category_name)

def main():
    """主函数"""
    print("=" * 50)
    print("利率综合领先指数构建 - 完整版")
    print("=" * 50)
    
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 加载数据
    df_raw = load_data()
    
    print(f"\n可用指标列表:")
    for i, col in enumerate(df_raw.columns, 1):
        print(f"{i:2d}. {col}")
    
    # 第一步：处理各类指标并提取主成分
    print(f"\n{'='*20} 第一步：构建9大分类指数 {'='*20}")
    category_indices = pd.DataFrame(index=df_raw.index)
    
    for cat_name, indicators in CATEGORIES.items():
        print(f"\n处理类别: {cat_name}")
        cat_data = pd.DataFrame(index=df_raw.index)
        
        for indicator in indicators:
            if indicator in df_raw.columns:
                print(f"  处理指标: {indicator}")
                rule = PROCESSING_RULES.get(indicator, {'seasonal': False, 'transform': 'level'})
                processed = process_indicator(df_raw[indicator], rule)
                
                if len(processed) > 0:
                    cat_data[indicator] = processed.reindex(df_raw.index)
            else:
                print(f"  警告: 未找到指标 {indicator}")
        
        # 提取该类别的主成分
        category_index = extract_pca_component(cat_data, cat_name)
        category_indices[cat_name] = category_index
    
    print(f"\n9大分类指数构建完成！")
    
    # 第二步：构建2大分项指数
    print(f"\n{'='*20} 第二步：构建2大分项指数 {'='*20}")
    
    # 定义基本面和金融市场分类
    fundamental_categories = [
        '工业上游生产', '生产生活资料价格', '地产与建筑', 
        '耐用消费品', '就业预期', '交通运输', '出口贸易'
    ]
    financial_categories = ['流动性与政策预期', '风险偏好与资产比价']
    
    # 标准化分类指数
    valid_category_indices = category_indices.dropna()
    if len(valid_category_indices) > 0:
        scaler = StandardScaler()
        category_indices_scaled = pd.DataFrame(
            scaler.fit_transform(valid_category_indices),
            columns=valid_category_indices.columns,
            index=valid_category_indices.index
        ).reindex(df_raw.index)
    else:
        category_indices_scaled = category_indices
    
    # 构建分项指数
    main_indices = pd.DataFrame(index=df_raw.index)
    
    # 基本面分项指数
    fundamental_cols = [col for col in fundamental_categories if col in category_indices_scaled.columns]
    if fundamental_cols:
        main_indices['基本面分项指数'] = category_indices_scaled[fundamental_cols].mean(axis=1)
        print(f"基本面分项指数包含: {fundamental_cols}")
    
    # 金融市场分项指数
    financial_cols = [col for col in financial_categories if col in category_indices_scaled.columns]
    if financial_cols:
        main_indices['金融市场分项指数'] = category_indices_scaled[financial_cols].mean(axis=1)
        print(f"金融市场分项指数包含: {financial_cols}")
    
    print(f"2大分项指数构建完成！")
    
    # 第三步：构建综合指数
    print(f"\n{'='*20} 第三步：构建综合指数 {'='*20}")
    
    final_index = pd.DataFrame(index=df_raw.index)
    if len(main_indices.columns) > 0:
        final_index['利率综合领先指数'] = main_indices.mean(axis=1)
        print("综合指数构建完成！")
    else:
        print("警告：无法构建综合指数")
    
    print(f"\n{'='*20} 生成可视化图表 {'='*20}")

    # 图1: 9大分类指数走势
    if len(category_indices_scaled.columns) > 0:
        n_cats = len(category_indices_scaled.columns)
        rows = (n_cats + 2) // 3
        fig1, axes = plt.subplots(rows, 3, figsize=(20, 5*rows))
        if rows == 1:
            axes = axes.reshape(1, -1)
        axes = axes.flatten()

        for i, cat in enumerate(category_indices_scaled.columns):
            if i < len(axes):
                valid_data = category_indices_scaled[cat].dropna()
                if len(valid_data) > 0:
                    axes[i].plot(valid_data, linewidth=1.5)
                    axes[i].set_title(cat, fontsize=12)
                    axes[i].grid(True, alpha=0.3)
                    axes[i].tick_params(axis='x', rotation=45)

        # 隐藏多余的子图
        for i in range(n_cats, len(axes)):
            axes[i].set_visible(False)

        plt.suptitle('9大分类指数走势图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.show()

    # 图2: 2大分项指数对比
    if len(main_indices.columns) > 0:
        fig2, ax2 = plt.subplots(figsize=(15, 8))

        for col in main_indices.columns:
            valid_data = main_indices[col].dropna()
            if len(valid_data) > 0:
                ax2.plot(valid_data, label=col, linewidth=2)

        ax2.set_title('2大分项指数走势对比', fontsize=16, fontweight='bold')
        ax2.legend(fontsize=12)
        ax2.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

    # 图3: 综合指数 vs 10Y国债收益率
    if TARGET_VARIABLE in df_raw.columns and '利率综合领先指数' in final_index.columns:
        df_comparison = pd.concat([
            final_index['利率综合领先指数'],
            df_raw[TARGET_VARIABLE]
        ], axis=1).dropna()

        if len(df_comparison) > 10:
            # 标准化用于比较
            scaler_final = StandardScaler()
            df_comparison_scaled = pd.DataFrame(
                scaler_final.fit_transform(df_comparison),
                columns=df_comparison.columns,
                index=df_comparison.index
            )

            fig3, ax3 = plt.subplots(figsize=(15, 8))
            ax3.plot(df_comparison_scaled['利率综合领先指数'],
                    label='利率综合领先指数 (标准化)', color='red', linewidth=2.5)
            ax3.plot(df_comparison_scaled[TARGET_VARIABLE],
                    label='10Y国债收益率 (标准化)', color='blue', linewidth=2, alpha=0.8)

            ax3.set_title('利率综合领先指数 vs. 10年期国债收益率', fontsize=16, fontweight='bold')
            ax3.legend(fontsize=12)
            ax3.grid(True, alpha=0.3)
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.show()

            # 计算相关性
            correlation = df_comparison.corr().iloc[0, 1]
            print(f"利率领先指数与10Y国债收益率的相关性: {correlation:.3f}")

    # 导出结果
    print(f"\n{'='*20} 导出结果 {'='*20}")
    print(f"导出路径: {OUTPUT_FILE_PATH}")

    try:
        with pd.ExcelWriter(OUTPUT_FILE_PATH, engine='openpyxl') as writer:
            # 9大分类指数
            category_indices.to_excel(writer, sheet_name='9大分类指数')

            # 2大分项指数
            main_indices.to_excel(writer, sheet_name='2大分项指数')

            # 综合指数
            final_index.to_excel(writer, sheet_name='综合指数')

            # 与目标变量的对比
            if TARGET_VARIABLE in df_raw.columns:
                comparison_df = pd.concat([final_index, df_raw[TARGET_VARIABLE]], axis=1)
                comparison_df.to_excel(writer, sheet_name='综合指数与利率对比')

            # 指标分类信息
            category_info = pd.DataFrame([
                {'分类': cat, '指标': ', '.join(indicators)}
                for cat, indicators in CATEGORIES.items()
            ])
            category_info.to_excel(writer, sheet_name='指标分类信息', index=False)

        print("Excel文件导出成功！")

    except Exception as e:
        print(f"导出失败: {e}")

    print(f"\n{'='*50}")
    print("利率综合领先指数构建完成！")
    print(f"{'='*50}")

    # 输出汇总信息
    print(f"\n汇总信息:")
    print(f"- 数据时间范围: {df_raw.index.min().strftime('%Y-%m-%d')} 到 {df_raw.index.max().strftime('%Y-%m-%d')}")
    print(f"- 总数据点数: {len(df_raw)}")
    print(f"- 9大分类指数: {len(category_indices.columns)}个")
    print(f"- 2大分项指数: {len(main_indices.columns)}个")
    print(f"- 综合指数: {len(final_index.columns)}个")


if __name__ == '__main__':
    main()
