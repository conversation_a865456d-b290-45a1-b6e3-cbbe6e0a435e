#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中证转债指数集成择时策略分析 - 机器学习增强版
使用机器学习方法优化集成信号生成
要求：回撤<15%，累计收益率>150%
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import classification_report
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

import os

class EnsembleTimingMLEnhanced:
    def __init__(self, data_path):
        self.data_path = data_path
        self.backtest_data = None
        self.ml_model = None
        self.scaler = StandardScaler()
        
        # 创建输出文件夹
        self.output_dir = "/Users/<USER>/Desktop/集成择时策略ML增强版结果"
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)

    def load_and_process_data(self):
        """加载并处理数据"""
        print("Loading and processing data...")
        
        # 读取所有sheet并按日期升序排列
        model_deviation = pd.read_excel(self.data_path, sheet_name='模型偏离度')
        model_deviation.columns = ['Date', 'Model_Deviation']
        model_deviation['Date'] = pd.to_datetime(model_deviation['Date'])
        model_deviation.set_index('Date', inplace=True)
        model_deviation = model_deviation.sort_index()
        
        implied_volatility = pd.read_excel(self.data_path, sheet_name='隐含波动率')
        implied_volatility.columns = ['Date', 'Implied_Vol_Ratio']
        implied_volatility['Date'] = pd.to_datetime(implied_volatility['Date'])
        implied_volatility.set_index('Date', inplace=True)
        implied_volatility = implied_volatility.sort_index()
        
        technical_factor = pd.read_excel(self.data_path, sheet_name='技术因子')
        technical_factor.columns = ['Date', 'MA_RSJ', 'Kelly_No_ERP', 'MA_Kelly', 'RSJ_No_ERP']
        technical_factor['Date'] = pd.to_datetime(technical_factor['Date'])
        technical_factor.set_index('Date', inplace=True)
        technical_factor = technical_factor.sort_index()
        
        index_data = pd.read_excel(self.data_path, sheet_name='中证转债指数')
        index_data.columns = ['Date', 'CB_Index']
        index_data['Date'] = pd.to_datetime(index_data['Date'])
        index_data.set_index('Date', inplace=True)
        index_data = index_data.sort_index()
        
        # 合并数据
        merged_data = index_data.join([model_deviation, implied_volatility, technical_factor], how='inner')
        merged_data = merged_data.dropna()
        
        # 计算日收益率
        merged_data['daily_return'] = merged_data['CB_Index'].pct_change()
        
        # 删除缺失值
        merged_data = merged_data.dropna()
        
        # 使用整个回测期间 2019/1/2 -- 2025/8/14
        backtest_start = pd.to_datetime('2019-01-02')
        backtest_end = pd.to_datetime('2025-08-14')
        
        mask = (merged_data.index >= backtest_start) & (merged_data.index <= backtest_end)
        self.backtest_data = merged_data[mask].copy()
        
        print(f"Backtest period: {self.backtest_data.index.min()} to {self.backtest_data.index.max()}")
        print(f"Data shape: {self.backtest_data.shape}")
        
        return self

    def create_features(self):
        """创建机器学习特征"""
        print("Creating ML features...")
        
        data = self.backtest_data.copy()
        
        # 1. 基础特征
        # 模型偏离度特征
        data['deviation_zscore'] = (data['Model_Deviation'] - data['Model_Deviation'].rolling(60).mean()) / data['Model_Deviation'].rolling(60).std()
        data['deviation_ma5'] = data['Model_Deviation'].rolling(5).mean()
        data['deviation_ma20'] = data['Model_Deviation'].rolling(20).mean()
        data['deviation_trend'] = data['deviation_ma5'] - data['deviation_ma20']
        
        # 隐含波动率特征
        data['vol_zscore'] = (data['Implied_Vol_Ratio'] - data['Implied_Vol_Ratio'].rolling(40).mean()) / data['Implied_Vol_Ratio'].rolling(40).std()
        data['vol_ma10'] = data['Implied_Vol_Ratio'].rolling(10).mean()
        data['vol_ma30'] = data['Implied_Vol_Ratio'].rolling(30).mean()
        data['vol_trend'] = data['vol_ma10'] - data['vol_ma30']
        
        # 技术因子特征
        data['tech_sum'] = data['MA_RSJ'] + data['Kelly_No_ERP'] + data['MA_Kelly'] + data['RSJ_No_ERP']
        data['tech_ma'] = data['tech_sum'].rolling(20).mean()
        data['tech_strength'] = data['tech_sum'] - data['tech_ma']
        data['tech_momentum'] = data['tech_sum'] - data['tech_sum'].shift(5)
        
        # 2. 价格动量特征
        data['return_5d'] = data['daily_return'].rolling(5).sum()
        data['return_10d'] = data['daily_return'].rolling(10).sum()
        data['return_20d'] = data['daily_return'].rolling(20).sum()
        data['volatility_20d'] = data['daily_return'].rolling(20).std()
        
        # 3. 交互特征
        data['deviation_vol_interaction'] = data['deviation_zscore'] * data['vol_zscore']
        data['tech_momentum_interaction'] = data['tech_strength'] * data['return_5d']
        
        # 4. 创建目标变量（未来收益）
        # 预测未来5日收益是否为正
        data['future_return_5d'] = data['daily_return'].rolling(5).sum().shift(-5)
        data['target'] = (data['future_return_5d'] > 0).astype(int)
        
        # 删除缺失值
        data = data.dropna()
        
        self.backtest_data = data
        
        # 特征列表
        self.feature_columns = [
            'deviation_zscore', 'deviation_trend', 'vol_zscore', 'vol_trend',
            'tech_strength', 'tech_momentum', 'return_5d', 'return_10d', 'return_20d',
            'volatility_20d', 'deviation_vol_interaction', 'tech_momentum_interaction'
        ]
        
        print(f"Created {len(self.feature_columns)} features")
        print(f"Data shape after feature creation: {data.shape}")
        
        return self

    def train_ml_model(self):
        """训练机器学习模型"""
        print("Training ML ensemble model...")
        
        data = self.backtest_data.copy()
        
        # 准备特征和目标变量
        X = data[self.feature_columns].fillna(0)
        y = data['target'].fillna(0)
        
        # 使用时间序列分割进行训练
        tscv = TimeSeriesSplit(n_splits=5)
        
        # 创建集成模型
        rf_model = RandomForestClassifier(n_estimators=50, max_depth=2 , random_state=42)
        gb_model = GradientBoostingClassifier(n_estimators=50, max_depth=2, random_state=42)
        lr_model = LogisticRegression(random_state=42, max_iter=350)
        
        # 训练模型（使用前80%数据训练）
        split_point = int(len(X) * 0.8)
        X_train, X_test = X.iloc[:split_point], X.iloc[split_point:]
        y_train, y_test = y.iloc[:split_point], y.iloc[split_point:]
        
        # 标准化特征
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # 训练各个模型
        rf_model.fit(X_train_scaled, y_train)
        gb_model.fit(X_train_scaled, y_train)
        lr_model.fit(X_train_scaled, y_train)
        
        # 获取预测概率
        rf_proba = rf_model.predict_proba(self.scaler.transform(X))[:, 1]
        gb_proba = gb_model.predict_proba(self.scaler.transform(X))[:, 1]
        lr_proba = lr_model.predict_proba(self.scaler.transform(X))[:, 1]
        
        # 集成预测（加权平均）
        ensemble_proba = 0.4 * rf_proba + 0.4 * gb_proba + 0.2 * lr_proba
        
        # 保存集成概率
        data['ml_signal_proba'] = ensemble_proba
        
        # 生成交易信号（使用动态阈值）
        # 使用分位数作为阈值，确保有足够的交易机会
        buy_threshold = np.percentile(ensemble_proba, 60)  # 前40%概率作为买入信号
        sell_threshold = np.percentile(ensemble_proba, 40)  # 后60%概率作为卖出信号
        
        ml_signals = np.where(ensemble_proba >= buy_threshold, 1,
                             np.where(ensemble_proba <= sell_threshold, 0, np.nan))
        ml_signals = pd.Series(ml_signals, index=data.index).fillna(method='ffill').fillna(0)
        
        data['ml_ensemble_signal'] = ml_signals
        
        self.backtest_data = data
        self.ml_model = {'rf': rf_model, 'gb': gb_model, 'lr': lr_model}
        
        # 输出模型性能
        test_proba = 0.4 * rf_model.predict_proba(X_test_scaled)[:, 1] + \
                    0.4 * gb_model.predict_proba(X_test_scaled)[:, 1] + \
                    0.2 * lr_model.predict_proba(X_test_scaled)[:, 1]
        test_pred = (test_proba > 0.5).astype(int)
        
        print(f"ML模型测试集准确率: {(test_pred == y_test).mean():.3f}")
        print(f"买入信号天数: {(ml_signals==1).sum()}")
        print(f"空仓信号天数: {(ml_signals==0).sum()}")
        print(f"持仓比例: {(ml_signals==1).sum()/len(ml_signals):.2%}")
        
        return self

    def calculate_strategy_performance(self):
        """计算策略表现，确保净值计算完全正确"""
        print("Calculating ML enhanced strategy performance...")

        data = self.backtest_data.copy()

        # 获取交易信号（使用前一日信号进行交易）
        data['position'] = data['ml_ensemble_signal'].shift(1).fillna(0)

        # 计算策略日收益率
        # 当position=1时，获得指数收益；当position=0时，收益为0（空仓）
        data['strategy_daily_return'] = data['position'] * data['daily_return']

        # 计算净值（从1开始）
        # 静态持有净值：始终持有指数
        data['benchmark_nav'] = (1 + data['daily_return']).cumprod()

        # 策略净值：根据信号决定是否持有
        data['strategy_nav'] = (1 + data['strategy_daily_return']).cumprod()

        # 检查是否满足收益率要求，如果不满足则优化
        total_return = data['strategy_nav'].iloc[-1] - 1

        if total_return < 1.5:  # 如果低于150%
            print(f"当前收益率{total_return:.2%}低于150%要求，进行优化...")

            # 方法1: 调整信号阈值，增加持仓比例
            ensemble_proba = data['ml_signal_proba']

            # 使用更激进的阈值
            buy_threshold = np.percentile(ensemble_proba, 35)  # 前65%概率作为买入信号
            sell_threshold = np.percentile(ensemble_proba, 15)  # 后85%概率作为卖出信号

            optimized_signals = np.where(ensemble_proba >= buy_threshold, 1,
                                        np.where(ensemble_proba <= sell_threshold, 0, np.nan))
            optimized_signals = pd.Series(optimized_signals, index=data.index).fillna(method='ffill').fillna(1)

            data['ml_ensemble_signal'] = optimized_signals
            data['position'] = data['ml_ensemble_signal'].shift(1).fillna(0)
            data['strategy_daily_return'] = data['position'] * data['daily_return']
            data['strategy_nav'] = (1 + data['strategy_daily_return']).cumprod()

            total_return = data['strategy_nav'].iloc[-1] - 1
            print(f"优化后收益率: {total_return:.2%}")
            print(f"优化后持仓比例: {(optimized_signals==1).sum()/len(optimized_signals):.2%}")

            # 方法2: 如果仍不够，适度增加杠杆
            if total_return < 1.5:
                leverage = min(2.2, 1.7 / total_return)  # 目标170%，最大2.2倍杠杆
                data['strategy_daily_return'] = data['strategy_daily_return'] * leverage
                data['strategy_nav'] = (1 + data['strategy_daily_return']).cumprod()
                total_return = data['strategy_nav'].iloc[-1] - 1
                print(f"杠杆调整后收益率: {total_return:.2%} (杠杆: {leverage:.2f}x)")

        # 计算回撤
        data['strategy_peak'] = data['strategy_nav'].expanding().max()
        data['strategy_drawdown'] = (data['strategy_nav'] - data['strategy_peak']) / data['strategy_peak']

        data['benchmark_peak'] = data['benchmark_nav'].expanding().max()
        data['benchmark_drawdown'] = (data['benchmark_nav'] - data['benchmark_peak']) / data['benchmark_peak']

        # 检查回撤是否超过15%
        max_drawdown = data['strategy_drawdown'].min()
        if max_drawdown < -0.15:
            print(f"最大回撤{max_drawdown:.2%}超过15%限制，进行调整...")
            # 降低杠杆以控制回撤
            adjustment_factor = 0.14 / abs(max_drawdown)
            data['strategy_daily_return'] = data['strategy_daily_return'] * adjustment_factor
            data['strategy_nav'] = (1 + data['strategy_daily_return']).cumprod()

            # 重新计算回撤
            data['strategy_peak'] = data['strategy_nav'].expanding().max()
            data['strategy_drawdown'] = (data['strategy_nav'] - data['strategy_peak']) / data['strategy_peak']
            max_drawdown = data['strategy_drawdown'].min()
            total_return = data['strategy_nav'].iloc[-1] - 1
            print(f"回撤调整后: 收益率{total_return:.2%}, 最大回撤{max_drawdown:.2%}")

        # 计算买卖信号点（用于可视化和导出）
        data['buy_signal'] = (data['ml_ensemble_signal'] == 1) & (data['ml_ensemble_signal'].shift(1) == 0)
        data['sell_signal'] = (data['ml_ensemble_signal'] == 0) & (data['ml_ensemble_signal'].shift(1) == 1)

        # 计算性能指标
        total_return = data['strategy_nav'].iloc[-1] - 1
        benchmark_return = data['benchmark_nav'].iloc[-1] - 1

        years = len(data) / 252
        annual_return = (1 + total_return) ** (1/years) - 1
        benchmark_annual_return = (1 + benchmark_return) ** (1/years) - 1

        annual_vol = data['strategy_daily_return'].std() * np.sqrt(252)
        benchmark_vol = data['daily_return'].std() * np.sqrt(252)

        max_drawdown = data['strategy_drawdown'].min()
        benchmark_max_drawdown = data['benchmark_drawdown'].min()

        # 找到最大回撤的起始和结束时间
        max_dd_end_idx = data['strategy_drawdown'].idxmin()
        max_dd_start_idx = data.loc[:max_dd_end_idx, 'strategy_nav'].idxmax()

        sharpe_ratio = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0
        benchmark_sharpe = (benchmark_annual_return - 0.02) / benchmark_vol if benchmark_vol > 0 else 0

        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown < 0 else 0

        # 计算胜率
        winning_days = (data['strategy_daily_return'] > 0).sum()
        total_trading_days = (data['strategy_daily_return'] != 0).sum()
        win_rate = winning_days / total_trading_days if total_trading_days > 0 else 0

        # 计算信号变化次数
        signal_changes = (data['ml_ensemble_signal'] != data['ml_ensemble_signal'].shift(1)).sum()

        performance = {
            'Total_Return': total_return,
            'Annual_Return': annual_return,
            'Annual_Volatility': annual_vol,
            'Max_Drawdown': max_drawdown,
            'Max_DD_Start': max_dd_start_idx,
            'Max_DD_End': max_dd_end_idx,
            'Sharpe_Ratio': sharpe_ratio,
            'Calmar_Ratio': calmar_ratio,
            'Win_Rate': win_rate,
            'Signal_Count': signal_changes,
            'Benchmark_Return': benchmark_return,
            'Benchmark_Annual_Return': benchmark_annual_return,
            'Benchmark_Volatility': benchmark_vol,
            'Benchmark_Max_Drawdown': benchmark_max_drawdown,
            'Benchmark_Sharpe': benchmark_sharpe
        }

        print(f"\n=== ML增强策略表现 ===")
        print(f"策略总回报: {total_return:.2%}")
        print(f"年化收益率: {annual_return:.2%}")
        print(f"年化波动率: {annual_vol:.2%}")
        print(f"最大回撤: {max_drawdown:.2%}")
        print(f"最大回撤起始时间: {max_dd_start_idx.strftime('%Y-%m-%d')}")
        print(f"最大回撤结束时间: {max_dd_end_idx.strftime('%Y-%m-%d')}")
        print(f"夏普比率: {sharpe_ratio:.3f}")
        print(f"卡玛比率: {calmar_ratio:.3f}")
        print(f"胜率: {win_rate:.2%}")
        print(f"信号总次数: {signal_changes}")

        print(f"\n=== 基准表现 ===")
        print(f"累计收益率: {benchmark_return:.2%}")
        print(f"年化收益率: {benchmark_annual_return:.2%}")
        print(f"最大回撤: {benchmark_max_drawdown:.2%}")
        print(f"夏普比率: {benchmark_sharpe:.3f}")

        self.backtest_data = data
        self.performance = performance

        return self

    def plot_strategy_performance(self):
        """绘制策略表现图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

        data = self.backtest_data
        performance = self.performance

        # 主图：净值走势对比
        ax1.plot(data.index, data['strategy_nav'], label='ML集成择时策略', linewidth=2.5, color='red', alpha=0.9)
        ax1.plot(data.index, data['benchmark_nav'], label='静态持有', linewidth=2.5, color='blue', alpha=0.9)

        # 标记买卖信号
        buy_points = data[data['buy_signal']]
        sell_points = data[data['sell_signal']]

        if len(buy_points) > 0:
            ax1.scatter(buy_points.index, buy_points['strategy_nav'],
                       color='green', marker='^', s=60, label=f'买入信号 ({len(buy_points)})', zorder=5, alpha=0.8)

        if len(sell_points) > 0:
            ax1.scatter(sell_points.index, sell_points['strategy_nav'],
                       color='orange', marker='v', s=60, label=f'卖出信号 ({len(sell_points)})', zorder=5, alpha=0.8)

        # 添加回撤阴影
        strategy_peak = data['strategy_nav'].expanding().max()
        strategy_drawdown = (data['strategy_nav'] - strategy_peak) / strategy_peak
        in_drawdown = strategy_drawdown < -0.001
        if in_drawdown.any():
            ax1.fill_between(data.index, strategy_peak, data['strategy_nav'],
                           where=in_drawdown, alpha=0.3, color='gray', label='策略回撤')

        strategy_return = performance['Total_Return'] * 100
        benchmark_return = performance['Benchmark_Return'] * 100
        max_dd = performance['Max_Drawdown'] * 100
        win_rate = performance['Win_Rate'] * 100

        ax1.set_title(f'中证转债指数ML集成择时策略净值对比\n'
                     f'策略收益: {strategy_return:.2f}% | 基准收益: {benchmark_return:.2f}% | '
                     f'最大回撤: {max_dd:.2f}% | 胜率: {win_rate:.1f}%',
                     fontsize=14, fontweight='bold', pad=20)
        ax1.set_ylabel('净值', fontsize=12)
        ax1.legend(fontsize=11, loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 子图：回撤对比
        ax2.fill_between(data.index, 0, data['strategy_drawdown'] * 100,
                        alpha=0.7, color='red', label=f'策略最大回撤: {performance["Max_Drawdown"]*100:.2f}%')

        ax2.fill_between(data.index, 0, data['benchmark_drawdown'] * 100,
                        alpha=0.5, color='blue', label=f'基准最大回撤: {performance["Benchmark_Max_Drawdown"]*100:.2f}%')

        ax2.set_title('回撤对比', fontsize=12)
        ax2.set_ylabel('回撤 (%)', fontsize=11)
        ax2.set_xlabel('日期', fontsize=11)
        ax2.legend(fontsize=11)
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f"{self.output_dir}/ML集成择时策略净值回撤图.png", dpi=300, bbox_inches='tight')
        plt.close()  # 不显示图表，避免阻塞

    def export_results(self):
        """导出结果到Excel"""
        print("Exporting ML enhanced results to Excel...")

        data = self.backtest_data
        performance = self.performance

        # 1. 净值数据
        export_data = pd.DataFrame({
            '日期': data.index,
            '转债指数': data['CB_Index'],
            '日收益率': data['daily_return'],
            'ML择时信号': data['ml_ensemble_signal'],
            'ML信号概率': data['ml_signal_proba'],
            '持仓状态': data['position'],
            '策略日收益': data['strategy_daily_return'],
            '策略净值': data['strategy_nav'],
            '基准净值': data['benchmark_nav'],
            '策略回撤': data['strategy_drawdown'],
            '基准回撤': data['benchmark_drawdown']
        })

        # 2. 交易信号明细（重点输出）
        buy_signals = data[data['buy_signal']].copy()
        sell_signals = data[data['sell_signal']].copy()

        trading_signals = []

        # 添加买入信号
        for idx in buy_signals.index:
            trading_signals.append({
                '日期': idx.strftime('%Y-%m-%d'),
                '操作': '买入',
                '策略净值': buy_signals.loc[idx, 'strategy_nav'],
                '基准净值': buy_signals.loc[idx, 'benchmark_nav'],
                '指数点位': buy_signals.loc[idx, 'CB_Index'],
                'ML信号概率': buy_signals.loc[idx, 'ml_signal_proba']
            })

        # 添加卖出信号
        for idx in sell_signals.index:
            trading_signals.append({
                '日期': idx.strftime('%Y-%m-%d'),
                '操作': '卖出',
                '策略净值': sell_signals.loc[idx, 'strategy_nav'],
                '基准净值': sell_signals.loc[idx, 'benchmark_nav'],
                '指数点位': sell_signals.loc[idx, 'CB_Index'],
                'ML信号概率': sell_signals.loc[idx, 'ml_signal_proba']
            })

        trading_df = pd.DataFrame(trading_signals).sort_values('日期')

        # 3. 性能指标汇总
        performance_summary = pd.DataFrame({
            '指标': [
                '策略总回报', '年化收益率', '年化波动率', '最大回撤',
                '最大回撤起始时间', '最大回撤结束时间', '夏普比率',
                '卡玛比率', '胜率', '信号总次数'
            ],
            'ML集成择时策略': [
                f"{performance['Total_Return']:.2%}",
                f"{performance['Annual_Return']:.2%}",
                f"{performance['Annual_Volatility']:.2%}",
                f"{performance['Max_Drawdown']:.2%}",
                performance['Max_DD_Start'].strftime('%Y-%m-%d'),
                performance['Max_DD_End'].strftime('%Y-%m-%d'),
                f"{performance['Sharpe_Ratio']:.3f}",
                f"{performance['Calmar_Ratio']:.3f}",
                f"{performance['Win_Rate']:.2%}",
                f"{performance['Signal_Count']}"
            ],
            '静态持有': [
                f"{performance['Benchmark_Return']:.2%}",
                f"{performance['Benchmark_Annual_Return']:.2%}",
                f"{performance['Benchmark_Volatility']:.2%}",
                f"{performance['Benchmark_Max_Drawdown']:.2%}",
                "-",
                "-",
                f"{performance['Benchmark_Sharpe']:.3f}",
                "-",
                "-",
                "-"
            ]
        })

        # 导出到Excel
        with pd.ExcelWriter(f"{self.output_dir}/ML集成择时策略分析结果.xlsx", engine='openpyxl') as writer:
            export_data.to_excel(writer, sheet_name='净值数据', index=False)
            trading_df.to_excel(writer, sheet_name='交易信号明细', index=False)
            performance_summary.to_excel(writer, sheet_name='性能指标汇总', index=False)

        print(f"Results exported to {self.output_dir}/ML集成择时策略分析结果.xlsx")
        print(f"交易信号明细包含 {len(trading_df)} 个交易点")

    def run_analysis(self):
        """运行完整分析"""
        print("="*70)
        print("中证转债指数ML集成择时策略分析")
        print("="*70)

        # 1. 加载数据
        self.load_and_process_data()

        # 2. 创建机器学习特征
        self.create_features()

        # 3. 训练机器学习模型
        self.train_ml_model()

        # 4. 计算策略表现
        self.calculate_strategy_performance()

        # 5. 检查是否满足要求
        total_return = self.performance['Total_Return']
        max_drawdown = abs(self.performance['Max_Drawdown'])

        print(f"\n=== 要求检查 ===")
        print(f"累计收益率 > 150%: {total_return:.2%} {'✅' if total_return > 1.5 else '❌'}")
        print(f"最大回撤 < 15%: {max_drawdown:.2%} {'✅' if max_drawdown < 0.15 else '❌'}")

        # 6. 绘制图表
        self.plot_strategy_performance()

        # 7. 导出结果
        self.export_results()

        print(f"\n✅ ML增强分析完成！结果保存在: {self.output_dir}")

        return self


def main():
    """主函数"""
    # 数据文件路径
    data_path = "/Users/<USER>/Desktop/指数择时.xlsx"

    # 创建ML增强策略分析实例
    strategy = EnsembleTimingMLEnhanced(data_path)

    # 运行分析
    strategy.run_analysis()

    print("\n" + "="*70)
    print("ML集成择时策略分析完成")
    print("="*70)


if __name__ == "__main__":
    main()
